﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.14">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="8.0.3" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.0.0" />
    <PackageReference Include="AspNetCore.HealthChecks.MySql" Version="8.0.1" />
    <PackageReference Include="AspNetCore.HealthChecks.RabbitMQ" Version="8.0.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\SlzrCrossGate.Core\SlzrCrossGate.Core.csproj" />
    <ProjectReference Include="..\SlzrCrossGate.ServiceDefaults\SlzrCrossGate.ServiceDefaults.csproj" />
    <ProjectReference Include="..\SlzrCrossGate.Tcp\SlzrCrossGate.Tcp.csproj" />
  </ItemGroup>

</Project>
