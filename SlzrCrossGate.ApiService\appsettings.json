{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Server=localhost;Port=3306;Database=tcpserver;User=root;Password=**********;SslMode=Required;AllowLoadLocalInfile=true;"}, "DatabaseProvider": "MySql", "RabbitMQ": {"HostName": "localhost", "UserName": "guest", "Password": "guest", "Port": 5672}, "FileService": {"DefaultStorageType": "Local", "LocalFilePath": "C:\\Files", "MinIO": {"Endpoint": "minio.example.com", "AccessKey": "your-access-key", "SecretKey": "your-secret-key", "BucketName": "your-bucket-name"}}}