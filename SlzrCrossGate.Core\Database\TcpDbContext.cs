using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using SlzrCrossGate.Core.Models;
using Microsoft.AspNetCore.Identity;


namespace SlzrCrossGate.Core.Database
{
    public class TcpDbContext : IdentityDbContext<ApplicationUser, ApplicationRole, string>
    {
        public TcpDbContext(DbContextOptions<TcpDbContext> options) : base(options)
        {

        }

        public DbSet<Merchant> Merchants { get; set; }
        public DbSet<FilePublish> FilePublishs { get; set; }
        public DbSet<FilePublishHistory> FilePublishHistories { get; set; }
        public DbSet<ScheduledFilePublish> ScheduledFilePublishs { get; set; }
        public DbSet<FileVer> FileVers { get; set; }
        public DbSet<FileType> FileTypes { get; set; }
        public DbSet<MsgType> MsgTypes { get; set; }
        public DbSet<MsgContent> MsgContents { get; set; }
        public DbSet<MsgBox> MsgBoxes { get; set; }
        public DbSet<Terminal> Terminals { get; set; }
        public DbSet<TerminalEvent> TerminalEvents { get; set; }
        public DbSet<TerminalStatus> TerminalStatuses { get; set; }
        public DbSet<UploadFile> UploadFiles { get; set; }
        public DbSet<TerminalFileUpload> TerminalFileUploads { get; set; }
        public DbSet<TerminalLog> TerminalLogs { get; set; }
        public DbSet<ConsumeData> ConsumeDatas { get; set; }
        public DbSet<UnionPayTerminalKey> UnionPayTerminalKeys { get; set; }
        public DbSet<IncrementContent> IncrementContents { get; set; }        public DbSet<MerchantDictionary> MerchantDictionaries { get; set; }
        public DbSet<LinePriceInfo> LinePriceInfos { get; set; }
        public DbSet<LinePriceInfoVersion> LinePriceInfoVersions { get; set; }
        public DbSet<FareDiscountScheme> FareDiscountSchemes { get; set; }
        public DbSet<FareDiscountSchemeVersion> FareDiscountSchemeVersions { get; set; }
        public DbSet<VehicleInfo> VehicleInfos { get; set; }

        public DbSet<ApplicationUser> ApplicationUsers { get; set; }
        public DbSet<ApplicationRole> ApplicationRoles { get; set; }
        public DbSet<SystemSettings> SystemSettings { get; set; }

        // 审计日志表
        public DbSet<LoginLog> LoginLogs { get; set; }
        public DbSet<PasswordChangeLog> PasswordChangeLogs { get; set; }
        public DbSet<OperationLog> OperationLogs { get; set; }

        // 菜单管理表
        public DbSet<MenuGroup> MenuGroups { get; set; }
        public DbSet<MenuItem> MenuItems { get; set; }

        // 功能权限管理表
        public DbSet<FeatureConfig> FeatureConfigs { get; set; }
        public DbSet<RoleFeaturePermission> RoleFeaturePermissions { get; set; }


        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // 配置Identity表的字符串长度限制，解决MySQL utf8mb4字符集下索引长度超限问题
            // MySQL索引键长度限制为767字节，utf8mb4字符集下每个字符占4字节
            // 因此字符串长度不能超过191个字符 (767/4=191.75)

            // 配置ApplicationUser表
            modelBuilder.Entity<ApplicationUser>(entity =>
            {
                entity.Property(e => e.Id).HasMaxLength(128);
                entity.Property(e => e.UserName).HasMaxLength(128);
                entity.Property(e => e.NormalizedUserName).HasMaxLength(128);
                entity.Property(e => e.Email).HasMaxLength(128);
                entity.Property(e => e.NormalizedEmail).HasMaxLength(128);
            });

            // 配置ApplicationRole表
            modelBuilder.Entity<ApplicationRole>(entity =>
            {
                entity.Property(e => e.Id).HasMaxLength(128);
                entity.Property(e => e.Name).HasMaxLength(128);
                entity.Property(e => e.NormalizedName).HasMaxLength(128);
            });

            // 配置IdentityUserRole表
            modelBuilder.Entity<IdentityUserRole<string>>(entity =>
            {
                entity.Property(e => e.UserId).HasMaxLength(128);
                entity.Property(e => e.RoleId).HasMaxLength(128);
            });

            // 配置IdentityUserClaim表
            modelBuilder.Entity<IdentityUserClaim<string>>(entity =>
            {
                entity.Property(e => e.UserId).HasMaxLength(128);
            });

            // 配置IdentityUserLogin表
            modelBuilder.Entity<IdentityUserLogin<string>>(entity =>
            {
                entity.Property(e => e.LoginProvider).HasMaxLength(128);
                entity.Property(e => e.ProviderKey).HasMaxLength(128);
                entity.Property(e => e.UserId).HasMaxLength(128);
            });

            // 配置IdentityRoleClaim表
            modelBuilder.Entity<IdentityRoleClaim<string>>(entity =>
            {
                entity.Property(e => e.RoleId).HasMaxLength(128);
            });

            // 配置IdentityUserToken表
            modelBuilder.Entity<IdentityUserToken<string>>(entity =>
            {
                entity.Property(e => e.UserId).HasMaxLength(128);
                entity.Property(e => e.LoginProvider).HasMaxLength(128);
                entity.Property(e => e.Name).HasMaxLength(128);
            });


            modelBuilder.Entity<ConsumeData>(builder=>
            {
                builder.HasKey(e => new { e.Id }).IsClustered();
                builder.HasIndex(c => c.ReceiveTime)
                    .IncludeProperties(c => new { c.MerchantID,c.MachineID,c.MachineNO });

            });

            modelBuilder.Entity<FilePublish>(builder =>
            {
                builder.HasKey(e => new { e.ID }).IsClustered();
                builder.HasIndex(e => new { e.MerchantID, e.FileTypeID, e.FilePara, e.PublishType, e.PublishTarget }).IsUnique();
                builder.HasIndex(e => new { e.MerchantID, e.FileFullType, e.PublishType, e.PublishTarget }).IsUnique();
                builder.HasIndex(e => new { e.MerchantID, e.FileTypeID, e.PublishTime });
                builder.HasIndex(e => new { e.PublishTime })
                    .IsDescending([true])
                    .IncludeProperties(e => new { e.MerchantID, e.FileTypeID, e.Ver, e.FilePara, e.FileFullType, e.PublishTarget });
            });

            modelBuilder.Entity<FilePublishHistory>(builder=>
            {
                builder.HasKey(e => new { e.ID }).IsClustered();
                //给FilePublishHistory的OperationType字段添加check约束，限制值只能为"Publish"或"Revoke"
                builder.ToTable(t => t.HasCheckConstraint("CK_FilePublishHistory_OperationType", "OperationType IN ('Publish', 'Revoke')"));
                builder.HasIndex(e => new { e.MerchantID, e.FileTypeID, e.FilePara, e.Ver });
                builder.HasIndex(e => new { e.PublishTime })
                    .IsDescending([true])
                    .IncludeProperties(e => new { e.MerchantID, e.FileTypeID, e.Ver, e.FilePara, e.FileFullType, e.PublishTarget, e.OperationType });
            });

            modelBuilder.Entity<FileVer>(builder=>
            {
                builder.HasKey(e => new { e.ID }).IsClustered();
                builder.HasIndex(e => new { e.MerchantID, e.FileFullType, e.Ver });
                builder.HasIndex(e => new { e.MerchantID, e.FileTypeID, e.FilePara, e.Ver });
                builder.HasIndex(e => new { e.CreateTime })
                    .IsDescending([true])
                    .IncludeProperties(e => new { e.MerchantID, e.FileTypeID, e.Ver, e.FilePara, e.FileFullType });
            });


            modelBuilder.Entity<FileType>(builder=>{
                builder.HasKey(e => new { e.ID, e.MerchantID }).IsClustered();
            });


            modelBuilder.Entity<IncrementContent>(builder=>
            {
                builder.HasKey(e => new { e.MerchantID, e.IncrementType, e.SerialNum })
                    .IsClustered();
            });

            modelBuilder.Entity<MsgType>(builder=>{
                builder.HasKey(e => new { e.ID, e.MerchantID }).IsClustered();
            });

            modelBuilder.Entity<Merchant>(builder=>{
                builder.HasKey(e=>e.MerchantID).IsClustered();
            });

            modelBuilder.Entity<MsgContent>(builder=>
            {
                builder.HasKey(e => e.ID).IsClustered();
                builder.HasIndex(e => new { e.MerchantID, e.CreateTime })
                    .IsDescending([false, true]);
            });

            modelBuilder.Entity<MsgBox>(builder=>
            {
                builder.HasKey(e => new { e.ID }).IsClustered();
                builder.HasIndex(e => new { e.MerchantID, e.Status, e.TerminalID, e.SendTime })
                    .IsDescending([false, false, false, false]);
                builder.HasIndex(e => new { e.SendTime })
                    .IsDescending([true])
                    .IncludeProperties(e => new { e.MerchantID, e.Status, e.TerminalID, e.MsgContentID });
            });


            modelBuilder.Entity<Terminal>(builder=>
            {
                builder.HasKey(e => new { e.ID }).IsClustered();
                builder.HasIndex(e => new { e.MerchantID, e.MachineID });
                builder.HasIndex(e => new { e.MerchantID, e.DeviceNO });
            });

            modelBuilder.Entity<TerminalEvent>(builder=> {
                builder.HasKey(e => e.ID).IsClustered();
                builder.HasIndex(e => new {e.TerminalID, e.EventTime })
                    .IsDescending([false, true])
                    .IncludeProperties(e => new { e.MerchantID, e.EventType });
                builder.HasIndex(e => new { e.EventTime })
                    .IsDescending([true])
                    .IncludeProperties(e => new { e.MerchantID, e.TerminalID, e.EventType });
            });

            modelBuilder.Entity<TerminalLog>(builder =>
            {
                builder.HasKey(e => e.ID).IsClustered();
                // 按商户和记录时间查询的索引
                builder.HasIndex(e => new { e.MerchantID, e.LogTime })
                    .IsDescending([false, true]);
                // 按记录类型查询的索引
                builder.HasIndex(e => new { e.LogType, e.LogTime })
                    .IsDescending([false, true]);
                // 按设备序列号查询的索引
                builder.HasIndex(e => new { e.MachineID, e.LogTime })
                    .IsDescending([false, true]);
                // 按设备编号查询的索引
                builder.HasIndex(e => new { e.MachineNO, e.LogTime })
                    .IsDescending([false, true]);
                // 按线路号查询的索引
                builder.HasIndex(e => new { e.LineNO, e.LogTime })
                    .IsDescending([false, true]);
                // 按卡号查询的索引
                builder.HasIndex(e => new { e.CardNO, e.LogTime })
                    .IsDescending([false, true]);
                // 按记录时间查询的索引
                builder.HasIndex(e => new { e.LogTime })
                    .IsDescending([true]);
            });

            modelBuilder.Entity<TerminalStatus>(builder =>
            {
                builder.HasKey(e => new { e.ID }).IsClustered();

                // 配置默认值，解决EFCore.BulkExtensions临时表问题
                builder.Property(e => e.ActiveStatus)
                    .HasDefaultValue(DeviceActiveStatus.Active); // 使用Inactive避免与CLR默认值冲突
                builder.Property(e => e.LoginInTime)
                    .HasDefaultValue(DateTime.MinValue);
                builder.Property(e => e.LoginOffTime)
                    .HasDefaultValue(DateTime.MinValue);
                builder.Property(e => e.LastActiveTime)
                    .HasDefaultValue(DateTime.MinValue);

                // MySQL 配置 json字段配置
                if (Database.IsMySql())
                {
                    builder.Property(e => e.FileVersions)
                        .HasColumnType("json");
                    builder.Property(e => e.Properties)
                        .HasColumnType("json");
                }
            });

            modelBuilder.Entity<UploadFile>(builder=>
            {
                builder.HasKey(e => new { e.ID }).IsClustered();
            });

            modelBuilder.Entity<TerminalFileUpload>(builder =>
            {
                builder.HasKey(e => e.ID).IsClustered();

                // 添加索引
                builder.HasIndex(e => new { e.MerchantID, e.TerminalID });
                builder.HasIndex(e => e.Status);
                builder.HasIndex(e => e.StartTime);
                builder.HasIndex(e => e.LastActivityTime);
                builder.HasIndex(e => new {e.TerminalID,e.StartTime });

                // 配置外键关系
                builder.HasOne(e => e.FinalUploadFile)
                    .WithMany()
                    .HasForeignKey(e => e.FinalUploadFileID)
                    .OnDelete(DeleteBehavior.SetNull);
            });

            modelBuilder.Entity<UnionPayTerminalKey>(builder=>
            {
                builder.HasKey(e => new { e.ID }).IsClustered();
                builder.HasIndex(e => new { e.MerchantID, e.MachineID });
                builder.HasIndex(e => new { e.MerchantID, e.IsInUse });
                builder.HasIndex(e => new { e.UP_MerchantID, e.UP_TerminalID })
                    .IsUnique();
            });

            modelBuilder.Entity<SystemSettings>(builder=>
            {
                builder.HasKey(e => new { e.Id }).IsClustered();
            });

            modelBuilder.Entity<MerchantDictionary>(builder=>
            {
                builder.HasKey(e => new { e.ID }).IsClustered();
                // 复合唯一索引：(MerchantID, DictionaryType, DictionaryCode)
                builder.HasIndex(e => new { e.MerchantID, e.DictionaryType, e.DictionaryCode }).IsUnique();
                // 辅助索引：按商户和字典类型查询
                builder.HasIndex(e => new { e.MerchantID, e.DictionaryType });
                // 外键关系
                builder.HasOne(e => e.Merchant)
                      .WithMany()
                      .HasForeignKey(e => e.MerchantID)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<LinePriceInfo>(builder =>
            {
                builder.HasKey(e => new { e.ID }).IsClustered();
                // 复合唯一索引：(MerchantID, LineNumber, GroupNumber)
                builder.HasIndex(e => new { e.MerchantID, e.LineNumber, e.GroupNumber }).IsUnique();
                // 外键关系
                builder.HasOne(e => e.Merchant)
                      .WithMany()
                      .HasForeignKey(e => e.MerchantID)
                      .OnDelete(DeleteBehavior.Cascade);
                builder.HasOne(e => e.CurrentFareDiscountScheme)
                      .WithMany()
                      .HasForeignKey(e => e.CurrentFareDiscountSchemeID)
                      .OnDelete(DeleteBehavior.SetNull);
            });

            modelBuilder.Entity<LinePriceInfoVersion>(builder =>
            {
                builder.HasKey(e => new { e.ID }).IsClustered();
                // 索引：按商户和线路信息ID查询
                builder.HasIndex(e => new { e.MerchantID, e.LinePriceInfoID });
                // 索引：按商户、线路号和组号查询
                builder.HasIndex(e => new { e.MerchantID, e.LineNumber, e.GroupNumber });
                // 索引：按版本状态查询
                builder.HasIndex(e => new { e.MerchantID, e.Status });
                // 外键关系
                builder.HasOne(e => e.Merchant)
                      .WithMany()
                      .HasForeignKey(e => e.MerchantID)
                      .OnDelete(DeleteBehavior.Cascade);
                builder.HasOne(e => e.LinePriceInfo)
                      .WithMany()
                      .HasForeignKey(e => e.LinePriceInfoID)
                      .OnDelete(DeleteBehavior.Cascade);
                builder.HasOne(e => e.FareDiscountScheme)
                      .WithMany()
                      .HasForeignKey(e => e.FareDiscountSchemeID)
                      .OnDelete(DeleteBehavior.SetNull);

                // MySQL 配置 json字段配置
                if (Database.IsMySql())
                {
                    builder.Property(e => e.ExtraParamsJson)
                        .HasColumnType("json");
                    builder.Property(e => e.CardDiscountInfoJson)
                        .HasColumnType("json");
                    builder.Property(e => e.FileContentJson)
                        .HasColumnType("json");
                }
            });

            modelBuilder.Entity<FareDiscountScheme>(builder =>
            {
                builder.HasKey(e => new { e.ID }).IsClustered();
                // 复合唯一索引：(MerchantID, SchemeCode)
                builder.HasIndex(e => new { e.MerchantID, e.SchemeCode }).IsUnique();
                // 索引：按商户和状态查询
                builder.HasIndex(e => new { e.MerchantID, e.IsActive });
                // 索引：按使用次数查询（用于统计）
                builder.HasIndex(e => new { e.MerchantID, e.UsageCount });
                // 外键关系
                builder.HasOne(e => e.Merchant)
                      .WithMany()
                      .HasForeignKey(e => e.MerchantID)
                      .OnDelete(DeleteBehavior.Cascade);

                // MySQL 配置 json字段配置
                if (Database.IsMySql())
                {
                    builder.Property(e => e.ExtraParamsJson)
                        .HasColumnType("json");
                    builder.Property(e => e.CardDiscountInfoJson)
                        .HasColumnType("json");
                }
            });

            modelBuilder.Entity<FareDiscountSchemeVersion>(builder =>
            {
                builder.HasKey(e => new { e.ID }).IsClustered();
                // 索引：按商户和方案ID查询
                builder.HasIndex(e => new { e.MerchantID, e.FareDiscountSchemeID });
                // 索引：按版本状态查询
                builder.HasIndex(e => new { e.MerchantID, e.Status });
                // 索引：按创建时间查询
                builder.HasIndex(e => new { e.CreateTime }).IsDescending();
                // 外键关系
                builder.HasOne(e => e.Merchant)
                      .WithMany()
                      .HasForeignKey(e => e.MerchantID)
                      .OnDelete(DeleteBehavior.Cascade);
                builder.HasOne(e => e.FareDiscountScheme)
                      .WithMany()
                      .HasForeignKey(e => e.FareDiscountSchemeID)
                      .OnDelete(DeleteBehavior.Cascade);

                // MySQL 配置 json字段配置
                if (Database.IsMySql())
                {
                    builder.Property(e => e.ExtraParamsJson)
                        .HasColumnType("json");
                    builder.Property(e => e.CardDiscountInfoJson)
                        .HasColumnType("json");
                    builder.Property(e => e.FileContentJson)
                        .HasColumnType("json");
                }
            });

            // 配置审计日志表
            modelBuilder.Entity<LoginLog>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => new { e.UserId, e.OperationTime });
                entity.HasIndex(e => new { e.MerchantId, e.OperationTime });
                entity.HasIndex(e => new { e.OperationTime }).IsDescending();
                entity.HasIndex(e => new { e.LoginType, e.OperationTime });
                entity.Property(e => e.OperationTime).HasDefaultValue(DateTime.Now);
            });

            modelBuilder.Entity<PasswordChangeLog>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => new { e.UserId, e.OperationTime });
                entity.HasIndex(e => new { e.MerchantId, e.OperationTime });
                entity.HasIndex(e => new { e.OperationTime }).IsDescending();
                entity.HasIndex(e => new { e.ChangeType, e.OperationTime });
                entity.Property(e => e.OperationTime).HasDefaultValue(DateTime.Now);
            });

            modelBuilder.Entity<OperationLog>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => new { e.UserId, e.OperationTime });
                entity.HasIndex(e => new { e.MerchantId, e.OperationTime });
                entity.HasIndex(e => new { e.OperationTime }).IsDescending();
                entity.HasIndex(e => new { e.Module, e.OperationType, e.OperationTime });
                entity.HasIndex(e => new { e.RequestPath, e.OperationTime });
                entity.Property(e => e.OperationTime).HasDefaultValue(DateTime.Now);
            });

            // 配置菜单管理表
            modelBuilder.Entity<MenuGroup>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.GroupKey).IsUnique();
                entity.HasIndex(e => e.SortOrder);
                entity.Property(e => e.CreatedAt).HasDefaultValue(DateTime.Now);
                entity.Property(e => e.UpdatedAt).HasDefaultValue(DateTime.Now);
            });

            modelBuilder.Entity<MenuItem>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => new { e.MenuGroupId, e.ItemKey }).IsUnique();
                entity.HasIndex(e => new { e.MenuGroupId, e.SortOrder });
                entity.Property(e => e.CreatedAt).HasDefaultValue(DateTime.Now);
                entity.Property(e => e.UpdatedAt).HasDefaultValue(DateTime.Now);

                // 外键关系
                entity.HasOne(e => e.MenuGroup)
                      .WithMany(g => g.MenuItems)
                      .HasForeignKey(e => e.MenuGroupId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // 配置车辆信息表
            modelBuilder.Entity<VehicleInfo>(entity =>
            {
                entity.HasKey(e => e.ID);

                // 设置唯一约束：同一商户下设备编号唯一
                entity.HasIndex(e => new { e.MerchantID, e.DeviceNO })
                      .IsUnique()
                      .HasDatabaseName("UK_Vehicle_MerchantDevice");

                // 设置唯一约束：同一商户下车牌号唯一
                entity.HasIndex(e => new { e.MerchantID, e.LicensePlate })
                      .IsUnique()
                      .HasDatabaseName("UK_Vehicle_MerchantLicense");

                // 设置其他索引
                entity.HasIndex(e => e.MerchantID)
                      .HasDatabaseName("IDX_Vehicle_Merchant");

                entity.HasIndex(e => e.LicensePlate)
                      .HasDatabaseName("IDX_Vehicle_License");

                entity.HasIndex(e => e.DriverName)
                      .HasDatabaseName("IDX_Vehicle_Driver");

                // 设置默认值
                entity.Property(e => e.CreateTime).HasDefaultValue(DateTime.Now);
                entity.Property(e => e.IsActive).HasDefaultValue(true);
                entity.Property(e => e.MaintenanceStatus).HasDefaultValue("Normal");
            });

            // 配置功能权限表
            modelBuilder.Entity<FeatureConfig>(entity =>
            {
                entity.HasKey(e => e.Id);

                // 设置唯一约束：功能键唯一
                entity.HasIndex(e => e.FeatureKey)
                      .IsUnique()
                      .HasDatabaseName("UK_FeatureConfig_Key");

                // 设置索引
                entity.HasIndex(e => e.Category)
                      .HasDatabaseName("IDX_FeatureConfig_Category");

                // 设置字段长度
                entity.Property(e => e.FeatureKey).HasMaxLength(100).IsRequired();
                entity.Property(e => e.FeatureName).HasMaxLength(100).IsRequired();
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.Category).HasMaxLength(50).IsRequired();
                entity.Property(e => e.RiskLevel).HasMaxLength(20).IsRequired().HasDefaultValue("Low");
                entity.Property(e => e.CreatedBy).HasMaxLength(50);
                entity.Property(e => e.UpdatedBy).HasMaxLength(50);

                // 设置默认值
                entity.Property(e => e.IsGloballyEnabled).HasDefaultValue(true);
                entity.Property(e => e.IsSystemBuiltIn).HasDefaultValue(false);
                entity.Property(e => e.SortOrder).HasDefaultValue(0);
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP(6)");
                entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)");
            });

            // 配置角色功能权限表
            modelBuilder.Entity<RoleFeaturePermission>(entity =>
            {
                entity.HasKey(e => e.Id);

                // 设置唯一约束：角色+功能键唯一
                entity.HasIndex(e => new { e.RoleName, e.FeatureKey })
                      .IsUnique()
                      .HasDatabaseName("UK_RoleFeaturePermission_RoleFeature");

                // 设置索引
                entity.HasIndex(e => e.RoleName)
                      .HasDatabaseName("IDX_RoleFeaturePermission_Role");

                entity.HasIndex(e => e.FeatureKey)
                      .HasDatabaseName("IDX_RoleFeaturePermission_Feature");

                // 设置字段长度
                entity.Property(e => e.RoleName).HasMaxLength(50).IsRequired();
                entity.Property(e => e.FeatureKey).HasMaxLength(100).IsRequired();
                entity.Property(e => e.CreatedBy).HasMaxLength(50);
                entity.Property(e => e.UpdatedBy).HasMaxLength(50);

                // 设置默认值
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP(6)");
                entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)");

                // 配置外键关系
                entity.HasOne(e => e.FeatureConfig)
                      .WithMany(f => f.RolePermissions)
                      .HasForeignKey(e => e.FeatureKey)
                      .HasPrincipalKey(f => f.FeatureKey)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            //// 配置租户隔离
            //foreach (var entityType in modelBuilder.Model.GetEntityTypes())
            //{
            //    if (typeof(ITenantEntity).IsAssignableFrom(entityType.ClrType))
            //    {
            //        modelBuilder.Entity(entityType.ClrType)
            //            .HasQueryFilter(GetTenantFilter(entityType.ClrType));
            //    }
            //}

            // 配置预约文件发布表
            modelBuilder.Entity<ScheduledFilePublish>(builder =>
            {
                builder.HasKey(e => e.ID).IsClustered();
                // 索引：按商户和状态查询
                builder.HasIndex(e => new { e.MerchantID, e.Status });
                // 索引：按预约时间查询（用于定时任务）
                builder.HasIndex(e => new { e.Status, e.ScheduledTime });
                // 索引：按创建时间查询
                builder.HasIndex(e => new { e.MerchantID, e.CreatedTime })
                    .IsDescending([false, true]);
            });
        }

        //// 租户隔离过滤器, 仅查询当前租户的数据,管理员可以查看所有数据
        //private LambdaExpression GetTenantFilter(Type entityType)
        //{
        //    var parameter = Expression.Parameter(entityType, "e");

        //    //如果是管理员，不过滤
        //    if (IsAdmin) return Expression.Lambda(Expression.Constant(true), parameter);

        //    var property = Expression.Property(parameter, "MerchantID");

        //    //不存在租户ID的数据，不过滤
        //    if (property == null) return Expression.Lambda(Expression.Constant(true), parameter);

        //    var currentTenantId = Expression.Constant(CurrentTenantId);
        //    var condition = Expression.Equal(property, currentTenantId);
        //    return Expression.Lambda(condition, parameter);

        //}
        //public string CurrentTenantId { get; set; }
        //public ApplicationUser CurrentUser { get; set; }

        //public bool IsAdmin { get; set; } = false;
    }
}
