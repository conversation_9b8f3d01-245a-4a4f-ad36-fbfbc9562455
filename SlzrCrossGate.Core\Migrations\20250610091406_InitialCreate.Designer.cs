﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using SlzrCrossGate.Core.Database;

#nullable disable

namespace SlzrCrossGate.Core.Migrations
{
    [DbContext(typeof(TcpDbContext))]
    [Migration("20250610091406_InitialCreate")]
    partial class InitialCreate
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.14")
                .HasAnnotation("Relational:MaxIdentifierLength", 64);

            MySqlModelBuilderExtensions.AutoIncrementColumns(modelBuilder);

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("longtext");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("longtext");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("longtext");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("longtext");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("ProviderKey")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("longtext");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("RoleId")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("LoginProvider")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Name")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Value")
                        .HasColumnType("longtext");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("SlzrCrossGate.Core.Models.ApplicationRole", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("longtext");

                    b.Property<bool>("IsSysAdmin")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Name")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex");

                    b.ToTable("AspNetRoles", (string)null);
                });

            modelBuilder.Entity("SlzrCrossGate.Core.Models.ApplicationUser", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("int");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("longtext");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Email")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("tinyint(1)");

                    b.Property<int?>("FailedTwoFactorAttempts")
                        .HasColumnType("int");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsTwoFactorRequired")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastFailedTwoFactorAttempt")
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("MerchantID")
                        .HasColumnType("longtext");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("longtext");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("longtext");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("RealName")
                        .HasColumnType("longtext");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("longtext");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("TwoFactorEnabledDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("TwoFactorSecretKey")
                        .HasColumnType("longtext");

                    b.Property<string>("UserName")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<DateTime?>("WechatBindTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("WechatNickname")
                        .HasColumnType("longtext");

                    b.Property<string>("WechatOpenId")
                        .HasColumnType("longtext");

                    b.Property<string>("WechatUnionId")
                        .HasColumnType("longtext");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex");

                    b.ToTable("AspNetUsers", (string)null);
                });

            modelBuilder.Entity("SlzrCrossGate.Core.Models.ConsumeData", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<long>("Id"));

                    b.Property<byte[]>("Buffer")
                        .IsRequired()
                        .HasMaxLength(2500)
                        .HasColumnType("varbinary(2500)");

                    b.Property<string>("MachineID")
                        .HasMaxLength(8)
                        .HasColumnType("varchar(8)");

                    b.Property<string>("MachineNO")
                        .HasMaxLength(8)
                        .HasColumnType("varchar(8)");

                    b.Property<string>("MerchantID")
                        .HasMaxLength(8)
                        .HasColumnType("varchar(8)");

                    b.Property<string>("PsamNO")
                        .HasMaxLength(12)
                        .HasColumnType("varchar(12)");

                    b.Property<DateTime>("ReceiveTime")
                        .HasColumnType("datetime(6)");

                    b.HasKey("Id")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.HasIndex("ReceiveTime")
                        .HasAnnotation("SqlServer:Include", new[] { "MerchantID", "MachineID", "MachineNO" });

                    b.ToTable("ConsumeDatas");
                });

            modelBuilder.Entity("SlzrCrossGate.Core.Models.FilePublish", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("ID"));

                    b.Property<string>("Crc")
                        .IsRequired()
                        .HasMaxLength(8)
                        .HasColumnType("varchar(8)");

                    b.Property<string>("FileFullType")
                        .IsRequired()
                        .HasMaxLength(11)
                        .HasColumnType("varchar(11)");

                    b.Property<string>("FilePara")
                        .IsRequired()
                        .HasMaxLength(8)
                        .HasColumnType("varchar(8)");

                    b.Property<int>("FileSize")
                        .HasColumnType("int");

                    b.Property<string>("FileTypeID")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("varchar(3)");

                    b.Property<int>("FileVerID")
                        .HasColumnType("int");

                    b.Property<string>("MerchantID")
                        .IsRequired()
                        .HasMaxLength(8)
                        .HasColumnType("varchar(8)");

                    b.Property<string>("Operator")
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.Property<string>("PublishTarget")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime>("PublishTime")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("PublishType")
                        .HasColumnType("int");

                    b.Property<string>("Remark")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("UploadFileID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("Ver")
                        .IsRequired()
                        .HasMaxLength(4)
                        .HasColumnType("char(4)");

                    b.HasKey("ID")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.HasIndex("MerchantID", "FileFullType", "Ver")
                        .IsUnique();

                    b.HasIndex("MerchantID", "FileTypeID", "PublishTime");

                    b.HasIndex("MerchantID", "FileTypeID", "FilePara", "Ver")
                        .IsUnique();

                    b.ToTable("FilePublishs");
                });

            modelBuilder.Entity("SlzrCrossGate.Core.Models.FilePublishHistory", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("ID"));

                    b.Property<string>("Crc")
                        .IsRequired()
                        .HasMaxLength(8)
                        .HasColumnType("varchar(8)");

                    b.Property<string>("FileFullType")
                        .IsRequired()
                        .HasMaxLength(11)
                        .HasColumnType("varchar(11)");

                    b.Property<string>("FilePara")
                        .IsRequired()
                        .HasMaxLength(8)
                        .HasColumnType("varchar(8)");

                    b.Property<int>("FileSize")
                        .HasColumnType("int");

                    b.Property<string>("FileTypeID")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("varchar(3)");

                    b.Property<int>("FileVerID")
                        .HasColumnType("int");

                    b.Property<string>("MerchantID")
                        .IsRequired()
                        .HasMaxLength(8)
                        .HasColumnType("varchar(8)");

                    b.Property<string>("OperationType")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("varchar(10)");

                    b.Property<string>("Operator")
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.Property<string>("PublishTarget")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime>("PublishTime")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("PublishType")
                        .HasColumnType("int");

                    b.Property<string>("Remark")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("UploadFileID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("Ver")
                        .IsRequired()
                        .HasMaxLength(4)
                        .HasColumnType("char(4)");

                    b.HasKey("ID")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.HasIndex("PublishTime")
                        .IsDescending()
                        .HasAnnotation("SqlServer:Include", new[] { "MerchantID", "FileTypeID", "Ver", "FilePara", "FileFullType", "PublishTarget", "OperationType" });

                    b.HasIndex("MerchantID", "FileTypeID", "FilePara", "Ver")
                        .IsUnique();

                    b.ToTable("FilePublishHistories", t =>
                        {
                            t.HasCheckConstraint("CK_FilePublishHistory_OperationType", "OperationType IN ('Publish', 'Revoke')");
                        });
                });

            modelBuilder.Entity("SlzrCrossGate.Core.Models.FileType", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(3)
                        .HasColumnType("varchar(3)");

                    b.Property<string>("MerchantID")
                        .HasMaxLength(8)
                        .HasColumnType("varchar(8)");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.HasKey("ID", "MerchantID")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.ToTable("FileTypes");
                });

            modelBuilder.Entity("SlzrCrossGate.Core.Models.FileVer", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("ID"));

                    b.Property<string>("Crc")
                        .IsRequired()
                        .HasMaxLength(8)
                        .HasColumnType("varchar(8)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("FileFullType")
                        .IsRequired()
                        .HasMaxLength(11)
                        .HasColumnType("varchar(11)");

                    b.Property<string>("FilePara")
                        .IsRequired()
                        .HasMaxLength(8)
                        .HasColumnType("varchar(8)");

                    b.Property<int>("FileSize")
                        .HasColumnType("int");

                    b.Property<string>("FileTypeID")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("varchar(3)");

                    b.Property<bool>("IsDelete")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("MerchantID")
                        .IsRequired()
                        .HasMaxLength(8)
                        .HasColumnType("varchar(8)");

                    b.Property<string>("Operator")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("UploadFileID")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("Ver")
                        .IsRequired()
                        .HasMaxLength(4)
                        .HasColumnType("varchar(4)");

                    b.HasKey("ID")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.HasIndex("MerchantID", "FileFullType", "Ver")
                        .IsUnique();

                    b.HasIndex("MerchantID", "FileTypeID", "FilePara", "Ver")
                        .IsUnique();

                    b.ToTable("FileVers");
                });

            modelBuilder.Entity("SlzrCrossGate.Core.Models.IncrementContent", b =>
                {
                    b.Property<string>("MerchantID")
                        .HasMaxLength(8)
                        .HasColumnType("varchar(8)");

                    b.Property<string>("IncrementType")
                        .HasMaxLength(4)
                        .HasColumnType("varchar(4)");

                    b.Property<int>("SerialNum")
                        .HasColumnType("int");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)");

                    b.HasKey("MerchantID", "IncrementType", "SerialNum")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.ToTable("IncrementContents");
                });

            modelBuilder.Entity("SlzrCrossGate.Core.Models.LinePriceInfo", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("ID"));

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Creator")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("CurrentVersion")
                        .IsRequired()
                        .HasMaxLength(4)
                        .HasColumnType("varchar(4)");

                    b.Property<int>("Fare")
                        .HasColumnType("int");

                    b.Property<string>("GroupNumber")
                        .IsRequired()
                        .HasMaxLength(2)
                        .HasColumnType("varchar(2)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("LineName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("LineNumber")
                        .IsRequired()
                        .HasMaxLength(4)
                        .HasColumnType("varchar(4)");

                    b.Property<string>("MerchantID")
                        .IsRequired()
                        .HasMaxLength(8)
                        .HasColumnType("varchar(8)");

                    b.Property<string>("Remark")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Updater")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.HasKey("ID")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.HasIndex("MerchantID", "LineNumber", "GroupNumber")
                        .IsUnique();

                    b.ToTable("LinePriceInfos");
                });

            modelBuilder.Entity("SlzrCrossGate.Core.Models.LinePriceInfoVersion", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("ID"));

                    b.Property<string>("CardDiscountInfoJson")
                        .HasColumnType("json");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Creator")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ExtraParamsJson")
                        .HasColumnType("json");

                    b.Property<int>("Fare")
                        .HasColumnType("int");

                    b.Property<string>("FileContentJson")
                        .HasColumnType("json");

                    b.Property<int?>("FileVerID")
                        .HasColumnType("int");

                    b.Property<string>("GroupNumber")
                        .IsRequired()
                        .HasMaxLength(2)
                        .HasColumnType("varchar(2)");

                    b.Property<bool>("IsPublished")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("LineName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("LineNumber")
                        .IsRequired()
                        .HasMaxLength(4)
                        .HasColumnType("varchar(4)");

                    b.Property<int>("LinePriceInfoID")
                        .HasColumnType("int");

                    b.Property<string>("MerchantID")
                        .IsRequired()
                        .HasMaxLength(8)
                        .HasColumnType("varchar(8)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<DateTime?>("SubmitTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Submitter")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Updater")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Version")
                        .IsRequired()
                        .HasMaxLength(4)
                        .HasColumnType("varchar(4)");

                    b.HasKey("ID")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.HasIndex("LinePriceInfoID");

                    b.HasIndex("MerchantID", "LinePriceInfoID");

                    b.HasIndex("MerchantID", "Status");

                    b.HasIndex("MerchantID", "LineNumber", "GroupNumber");

                    b.ToTable("LinePriceInfoVersions");
                });

            modelBuilder.Entity("SlzrCrossGate.Core.Models.Merchant", b =>
                {
                    b.Property<string>("MerchantID")
                        .HasMaxLength(8)
                        .HasColumnType("varchar(8)");

                    b.Property<bool>("AutoRegister")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("CompanyName")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("ContactInfo")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("ContactPerson")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<bool>("IsDelete")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Operator")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Remark")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.HasKey("MerchantID")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.ToTable("Merchants");
                });

            modelBuilder.Entity("SlzrCrossGate.Core.Models.MerchantDictionary", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("ID"));

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Creator")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<string>("DictionaryCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("DictionaryLabel")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("DictionaryType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("DictionaryValue")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<string>("ExtraValue1")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<string>("ExtraValue2")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("MerchantID")
                        .IsRequired()
                        .HasMaxLength(8)
                        .HasColumnType("varchar(8)");

                    b.Property<int>("SortOrder")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Updater")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.HasKey("ID")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.HasIndex("MerchantID", "DictionaryType");

                    b.HasIndex("MerchantID", "DictionaryType", "DictionaryCode")
                        .IsUnique();

                    b.ToTable("MerchantDictionaries");
                });

            modelBuilder.Entity("SlzrCrossGate.Core.Models.MsgBox", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("ID"));

                    b.Property<string>("MerchantID")
                        .IsRequired()
                        .HasMaxLength(8)
                        .HasColumnType("varchar(8)");

                    b.Property<int>("MsgContentID")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ReadTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ReplyCode")
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.Property<string>("ReplyContent")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.Property<DateTime?>("ReplyTime")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime>("SendTime")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("TerminalID")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.HasKey("ID")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.HasIndex("MerchantID", "Status", "TerminalID", "SendTime");

                    b.ToTable("MsgBoxes");
                });

            modelBuilder.Entity("SlzrCrossGate.Core.Models.MsgContent", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(4)
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("ID"));

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("MerchantID")
                        .IsRequired()
                        .HasMaxLength(8)
                        .HasColumnType("varchar(8)");

                    b.Property<string>("MsgTypeID")
                        .IsRequired()
                        .HasMaxLength(4)
                        .HasColumnType("varchar(4)");

                    b.Property<string>("Operator")
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.Property<string>("Remark")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.HasKey("ID")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.ToTable("MsgContents");
                });

            modelBuilder.Entity("SlzrCrossGate.Core.Models.MsgType", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(4)
                        .HasColumnType("varchar(4)");

                    b.Property<string>("MerchantID")
                        .HasMaxLength(8)
                        .HasColumnType("varchar(8)");

                    b.Property<int>("CodeType")
                        .HasMaxLength(10)
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<string>("ExampleMessage")
                        .HasMaxLength(1000)
                        .HasColumnType("varchar(1000)");

                    b.Property<string>("Name")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.HasKey("ID", "MerchantID")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.ToTable("MsgTypes");
                });

            modelBuilder.Entity("SlzrCrossGate.Core.Models.SystemSettings", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("EnableTwoFactorAuth")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("EnableWechatLogin")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("ForceTwoFactorAuth")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime>("LastModified")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("longtext");

                    b.HasKey("Id")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.ToTable("SystemSettings");
                });

            modelBuilder.Entity("SlzrCrossGate.Core.Models.Terminal", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("DeviceNO")
                        .IsRequired()
                        .HasMaxLength(8)
                        .HasColumnType("varchar(8)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("LineNO")
                        .IsRequired()
                        .HasMaxLength(8)
                        .HasColumnType("varchar(8)");

                    b.Property<string>("MachineID")
                        .IsRequired()
                        .HasMaxLength(8)
                        .HasColumnType("varchar(8)");

                    b.Property<string>("MerchantID")
                        .IsRequired()
                        .HasMaxLength(8)
                        .HasColumnType("varchar(8)");

                    b.Property<DateTime>("StatusUpdateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("TerminalType")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.HasKey("ID")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.HasIndex("MerchantID", "DeviceNO");

                    b.HasIndex("MerchantID", "MachineID");

                    b.ToTable("Terminals");
                });

            modelBuilder.Entity("SlzrCrossGate.Core.Models.TerminalEvent", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("ID"));

                    b.Property<string>("EventName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime>("EventTime")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("EventType")
                        .HasColumnType("int");

                    b.Property<string>("MerchantID")
                        .IsRequired()
                        .HasMaxLength(8)
                        .HasColumnType("varchar(8)");

                    b.Property<string>("Operator")
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.Property<string>("Remark")
                        .HasMaxLength(2000)
                        .HasColumnType("varchar(2000)");

                    b.Property<int>("Severity")
                        .HasColumnType("int");

                    b.Property<string>("TerminalID")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.HasKey("ID")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.HasIndex("EventTime")
                        .IsDescending();

                    b.HasIndex("MerchantID", "TerminalID", "EventTime")
                        .IsDescending(false, false, true);

                    b.HasIndex("MerchantID", "TerminalID", "EventType", "EventTime")
                        .IsDescending(false, false, false, true);

                    b.ToTable("TerminalEvents");
                });

            modelBuilder.Entity("SlzrCrossGate.Core.Models.TerminalStatus", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.Property<int>("ActiveStatus")
                        .HasColumnType("int");

                    b.Property<string>("ConnectionProtocol")
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.Property<string>("EndPoint")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("FileVersions")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("json");

                    b.Property<DateTime>("LastActiveTime")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime>("LoginInTime")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime>("LoginOffTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Properties")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("json");

                    b.Property<string>("Token")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.HasKey("ID")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.ToTable("TerminalStatuses");
                });

            modelBuilder.Entity("SlzrCrossGate.Core.Models.UnionPayTerminalKey", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("ID"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("IsInUse")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("LineID")
                        .HasMaxLength(8)
                        .HasColumnType("varchar(8)");

                    b.Property<string>("MachineID")
                        .HasMaxLength(8)
                        .HasColumnType("varchar(8)");

                    b.Property<string>("MachineNO")
                        .HasMaxLength(8)
                        .HasColumnType("varchar(8)");

                    b.Property<string>("MerchantID")
                        .IsRequired()
                        .HasMaxLength(8)
                        .HasColumnType("varchar(8)");

                    b.Property<string>("UP_Key")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("UP_MerchantID")
                        .IsRequired()
                        .HasMaxLength(15)
                        .HasColumnType("varchar(15)");

                    b.Property<string>("UP_MerchantName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("UP_TerminalID")
                        .IsRequired()
                        .HasMaxLength(8)
                        .HasColumnType("varchar(8)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)");

                    b.HasKey("ID")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.HasIndex("MerchantID", "IsInUse");

                    b.HasIndex("MerchantID", "MachineID");

                    b.HasIndex("UP_MerchantID", "UP_TerminalID")
                        .IsUnique();

                    b.ToTable("UnionPayTerminalKeys");
                });

            modelBuilder.Entity("SlzrCrossGate.Core.Models.UploadFile", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("BucketName")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("ContentType")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("Crc")
                        .IsRequired()
                        .HasMaxLength(8)
                        .HasColumnType("varchar(8)");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<int>("FileSize")
                        .HasColumnType("int");

                    b.Property<DateTime>("LastModifiedTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ObjectStorageUrl")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.Property<DateTime>("UploadTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("UploadedBy")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.HasKey("ID")
                        .HasAnnotation("SqlServer:Clustered", true);

                    b.ToTable("UploadFiles");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.HasOne("SlzrCrossGate.Core.Models.ApplicationRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.HasOne("SlzrCrossGate.Core.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.HasOne("SlzrCrossGate.Core.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.HasOne("SlzrCrossGate.Core.Models.ApplicationRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("SlzrCrossGate.Core.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.HasOne("SlzrCrossGate.Core.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("SlzrCrossGate.Core.Models.LinePriceInfo", b =>
                {
                    b.HasOne("SlzrCrossGate.Core.Models.Merchant", "Merchant")
                        .WithMany()
                        .HasForeignKey("MerchantID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Merchant");
                });

            modelBuilder.Entity("SlzrCrossGate.Core.Models.LinePriceInfoVersion", b =>
                {
                    b.HasOne("SlzrCrossGate.Core.Models.LinePriceInfo", "LinePriceInfo")
                        .WithMany()
                        .HasForeignKey("LinePriceInfoID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("SlzrCrossGate.Core.Models.Merchant", "Merchant")
                        .WithMany()
                        .HasForeignKey("MerchantID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("LinePriceInfo");

                    b.Navigation("Merchant");
                });

            modelBuilder.Entity("SlzrCrossGate.Core.Models.MerchantDictionary", b =>
                {
                    b.HasOne("SlzrCrossGate.Core.Models.Merchant", "Merchant")
                        .WithMany()
                        .HasForeignKey("MerchantID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Merchant");
                });

            modelBuilder.Entity("SlzrCrossGate.Core.Models.TerminalStatus", b =>
                {
                    b.HasOne("SlzrCrossGate.Core.Models.Terminal", "Terminal")
                        .WithOne("Status")
                        .HasForeignKey("SlzrCrossGate.Core.Models.TerminalStatus", "ID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Terminal");
                });

            modelBuilder.Entity("SlzrCrossGate.Core.Models.Terminal", b =>
                {
                    b.Navigation("Status");
                });
#pragma warning restore 612, 618
        }
    }
}
