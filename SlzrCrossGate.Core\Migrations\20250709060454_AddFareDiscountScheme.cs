﻿using System;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SlzrCrossGate.Core.Migrations
{
    /// <inheritdoc />
    public partial class AddFareDiscountScheme : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<DateTime>(
                name: "CreateTime",
                table: "VehicleInfos",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 9, 14, 4, 52, 457, DateTimeKind.Local).AddTicks(2383),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 7, 11, 22, 20, 448, DateTimeKind.Local).AddTicks(805));

            migrationBuilder.AlterColumn<DateTime>(
                name: "OperationTime",
                table: "PasswordChangeLogs",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 9, 14, 4, 52, 456, DateTimeKind.Local).AddTicks(6954),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 7, 11, 22, 20, 447, DateTimeKind.Local).AddTicks(5168));

            migrationBuilder.AlterColumn<DateTime>(
                name: "OperationTime",
                table: "OperationLogs",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 9, 14, 4, 52, 456, DateTimeKind.Local).AddTicks(8231),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 7, 11, 22, 20, 447, DateTimeKind.Local).AddTicks(6465));

            migrationBuilder.AlterColumn<DateTime>(
                name: "UpdatedAt",
                table: "MenuItems",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 9, 14, 4, 52, 457, DateTimeKind.Local).AddTicks(202),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 7, 11, 22, 20, 447, DateTimeKind.Local).AddTicks(8582));

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedAt",
                table: "MenuItems",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 9, 14, 4, 52, 456, DateTimeKind.Local).AddTicks(9875),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 7, 11, 22, 20, 447, DateTimeKind.Local).AddTicks(8237));

            migrationBuilder.AlterColumn<DateTime>(
                name: "UpdatedAt",
                table: "MenuGroups",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 9, 14, 4, 52, 456, DateTimeKind.Local).AddTicks(9101),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 7, 11, 22, 20, 447, DateTimeKind.Local).AddTicks(7341));

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedAt",
                table: "MenuGroups",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 9, 14, 4, 52, 456, DateTimeKind.Local).AddTicks(8845),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 7, 11, 22, 20, 447, DateTimeKind.Local).AddTicks(7062));

            migrationBuilder.AlterColumn<DateTime>(
                name: "OperationTime",
                table: "LoginLogs",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 9, 14, 4, 52, 456, DateTimeKind.Local).AddTicks(5845),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 7, 11, 22, 20, 447, DateTimeKind.Local).AddTicks(3929));

            migrationBuilder.AddColumn<int>(
                name: "FareDiscountSchemeID",
                table: "LinePriceInfoVersions",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SchemeName",
                table: "LinePriceInfoVersions",
                type: "varchar(100)",
                maxLength: 100,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<int>(
                name: "CurrentFareDiscountSchemeID",
                table: "LinePriceInfos",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CurrentSchemeName",
                table: "LinePriceInfos",
                type: "varchar(100)",
                maxLength: 100,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "FareDiscountSchemes",
                columns: table => new
                {
                    ID = table.Column<int>(type: "int", nullable: false)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    MerchantID = table.Column<string>(type: "varchar(8)", maxLength: 8, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    SchemeName = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    SchemeCode = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Description = table.Column<string>(type: "varchar(500)", maxLength: 500, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ExtraParamsJson = table.Column<string>(type: "json", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CardDiscountInfoJson = table.Column<string>(type: "json", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    IsActive = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    CreateTime = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    UpdateTime = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    Creator = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Updater = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UsageCount = table.Column<int>(type: "int", nullable: false),
                    LastUsedTime = table.Column<DateTime>(type: "datetime(6)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FareDiscountSchemes", x => x.ID);
                    table.ForeignKey(
                        name: "FK_FareDiscountSchemes_Merchants_MerchantID",
                        column: x => x.MerchantID,
                        principalTable: "Merchants",
                        principalColumn: "MerchantID",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_LinePriceInfoVersions_FareDiscountSchemeID",
                table: "LinePriceInfoVersions",
                column: "FareDiscountSchemeID");

            migrationBuilder.CreateIndex(
                name: "IX_LinePriceInfos_CurrentFareDiscountSchemeID",
                table: "LinePriceInfos",
                column: "CurrentFareDiscountSchemeID");

            migrationBuilder.CreateIndex(
                name: "IX_FareDiscountSchemes_MerchantID_IsActive",
                table: "FareDiscountSchemes",
                columns: new[] { "MerchantID", "IsActive" });

            migrationBuilder.CreateIndex(
                name: "IX_FareDiscountSchemes_MerchantID_SchemeCode",
                table: "FareDiscountSchemes",
                columns: new[] { "MerchantID", "SchemeCode" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_FareDiscountSchemes_MerchantID_UsageCount",
                table: "FareDiscountSchemes",
                columns: new[] { "MerchantID", "UsageCount" });

            migrationBuilder.AddForeignKey(
                name: "FK_LinePriceInfos_FareDiscountSchemes_CurrentFareDiscountScheme~",
                table: "LinePriceInfos",
                column: "CurrentFareDiscountSchemeID",
                principalTable: "FareDiscountSchemes",
                principalColumn: "ID",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_LinePriceInfoVersions_FareDiscountSchemes_FareDiscountScheme~",
                table: "LinePriceInfoVersions",
                column: "FareDiscountSchemeID",
                principalTable: "FareDiscountSchemes",
                principalColumn: "ID",
                onDelete: ReferentialAction.SetNull);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_LinePriceInfos_FareDiscountSchemes_CurrentFareDiscountScheme~",
                table: "LinePriceInfos");

            migrationBuilder.DropForeignKey(
                name: "FK_LinePriceInfoVersions_FareDiscountSchemes_FareDiscountScheme~",
                table: "LinePriceInfoVersions");

            migrationBuilder.DropTable(
                name: "FareDiscountSchemes");

            migrationBuilder.DropIndex(
                name: "IX_LinePriceInfoVersions_FareDiscountSchemeID",
                table: "LinePriceInfoVersions");

            migrationBuilder.DropIndex(
                name: "IX_LinePriceInfos_CurrentFareDiscountSchemeID",
                table: "LinePriceInfos");

            migrationBuilder.DropColumn(
                name: "FareDiscountSchemeID",
                table: "LinePriceInfoVersions");

            migrationBuilder.DropColumn(
                name: "SchemeName",
                table: "LinePriceInfoVersions");

            migrationBuilder.DropColumn(
                name: "CurrentFareDiscountSchemeID",
                table: "LinePriceInfos");

            migrationBuilder.DropColumn(
                name: "CurrentSchemeName",
                table: "LinePriceInfos");

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreateTime",
                table: "VehicleInfos",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 7, 11, 22, 20, 448, DateTimeKind.Local).AddTicks(805),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 9, 14, 4, 52, 457, DateTimeKind.Local).AddTicks(2383));

            migrationBuilder.AlterColumn<DateTime>(
                name: "OperationTime",
                table: "PasswordChangeLogs",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 7, 11, 22, 20, 447, DateTimeKind.Local).AddTicks(5168),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 9, 14, 4, 52, 456, DateTimeKind.Local).AddTicks(6954));

            migrationBuilder.AlterColumn<DateTime>(
                name: "OperationTime",
                table: "OperationLogs",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 7, 11, 22, 20, 447, DateTimeKind.Local).AddTicks(6465),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 9, 14, 4, 52, 456, DateTimeKind.Local).AddTicks(8231));

            migrationBuilder.AlterColumn<DateTime>(
                name: "UpdatedAt",
                table: "MenuItems",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 7, 11, 22, 20, 447, DateTimeKind.Local).AddTicks(8582),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 9, 14, 4, 52, 457, DateTimeKind.Local).AddTicks(202));

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedAt",
                table: "MenuItems",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 7, 11, 22, 20, 447, DateTimeKind.Local).AddTicks(8237),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 9, 14, 4, 52, 456, DateTimeKind.Local).AddTicks(9875));

            migrationBuilder.AlterColumn<DateTime>(
                name: "UpdatedAt",
                table: "MenuGroups",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 7, 11, 22, 20, 447, DateTimeKind.Local).AddTicks(7341),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 9, 14, 4, 52, 456, DateTimeKind.Local).AddTicks(9101));

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedAt",
                table: "MenuGroups",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 7, 11, 22, 20, 447, DateTimeKind.Local).AddTicks(7062),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 9, 14, 4, 52, 456, DateTimeKind.Local).AddTicks(8845));

            migrationBuilder.AlterColumn<DateTime>(
                name: "OperationTime",
                table: "LoginLogs",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 7, 11, 22, 20, 447, DateTimeKind.Local).AddTicks(3929),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 9, 14, 4, 52, 456, DateTimeKind.Local).AddTicks(5845));
        }
    }
}
