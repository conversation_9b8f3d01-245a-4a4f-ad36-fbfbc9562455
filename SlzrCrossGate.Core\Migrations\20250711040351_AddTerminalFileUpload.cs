﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SlzrCrossGate.Core.Migrations
{
    /// <inheritdoc />
    public partial class AddTerminalFileUpload : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<DateTime>(
                name: "CreateTime",
                table: "VehicleInfos",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 11, 12, 3, 51, 67, DateTimeKind.Local).AddTicks(465),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 9, 14, 4, 52, 457, DateTimeKind.Local).AddTicks(2383));

            migrationBuilder.AlterColumn<DateTime>(
                name: "OperationTime",
                table: "PasswordChangeLogs",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 11, 12, 3, 51, 66, DateTimeKind.Local).AddTicks(5638),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 9, 14, 4, 52, 456, DateTimeKind.Local).AddTicks(6954));

            migrationBuilder.AlterColumn<DateTime>(
                name: "OperationTime",
                table: "OperationLogs",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 11, 12, 3, 51, 66, DateTimeKind.Local).AddTicks(6693),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 9, 14, 4, 52, 456, DateTimeKind.Local).AddTicks(8231));

            migrationBuilder.AlterColumn<DateTime>(
                name: "UpdatedAt",
                table: "MenuItems",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 11, 12, 3, 51, 66, DateTimeKind.Local).AddTicks(8513),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 9, 14, 4, 52, 457, DateTimeKind.Local).AddTicks(202));

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedAt",
                table: "MenuItems",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 11, 12, 3, 51, 66, DateTimeKind.Local).AddTicks(8167),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 9, 14, 4, 52, 456, DateTimeKind.Local).AddTicks(9875));

            migrationBuilder.AlterColumn<DateTime>(
                name: "UpdatedAt",
                table: "MenuGroups",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 11, 12, 3, 51, 66, DateTimeKind.Local).AddTicks(7464),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 9, 14, 4, 52, 456, DateTimeKind.Local).AddTicks(9101));

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedAt",
                table: "MenuGroups",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 11, 12, 3, 51, 66, DateTimeKind.Local).AddTicks(7229),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 9, 14, 4, 52, 456, DateTimeKind.Local).AddTicks(8845));

            migrationBuilder.AlterColumn<DateTime>(
                name: "OperationTime",
                table: "LoginLogs",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 11, 12, 3, 51, 66, DateTimeKind.Local).AddTicks(4559),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 9, 14, 4, 52, 456, DateTimeKind.Local).AddTicks(5845));

            migrationBuilder.CreateTable(
                name: "TerminalFileUploads",
                columns: table => new
                {
                    ID = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    MerchantID = table.Column<string>(type: "varchar(8)", maxLength: 8, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    TerminalID = table.Column<string>(type: "varchar(20)", maxLength: 20, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    TerminalType = table.Column<string>(type: "varchar(10)", maxLength: 10, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    FileType = table.Column<string>(type: "varchar(10)", maxLength: 10, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    FileName = table.Column<string>(type: "varchar(255)", maxLength: 255, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    FileSize = table.Column<long>(type: "bigint", nullable: false),
                    FileCRC = table.Column<string>(type: "varchar(8)", maxLength: 8, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ReceivedLength = table.Column<long>(type: "bigint", nullable: false),
                    ClientChunkSize = table.Column<int>(type: "int", nullable: false),
                    ServerChunkSize = table.Column<int>(type: "int", nullable: false),
                    Status = table.Column<int>(type: "int", nullable: false),
                    StartTime = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    UpdateTime = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    TempFilePath = table.Column<string>(type: "varchar(500)", maxLength: 500, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    FinalUploadFileID = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ErrorMessage = table.Column<string>(type: "varchar(500)", maxLength: 500, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UploadedBy = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    LastActivityTime = table.Column<DateTime>(type: "datetime(6)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TerminalFileUploads", x => x.ID);
                    table.ForeignKey(
                        name: "FK_TerminalFileUploads_UploadFiles_FinalUploadFileID",
                        column: x => x.FinalUploadFileID,
                        principalTable: "UploadFiles",
                        principalColumn: "ID",
                        onDelete: ReferentialAction.SetNull);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_TerminalFileUploads_FinalUploadFileID",
                table: "TerminalFileUploads",
                column: "FinalUploadFileID");

            migrationBuilder.CreateIndex(
                name: "IX_TerminalFileUploads_LastActivityTime",
                table: "TerminalFileUploads",
                column: "LastActivityTime");

            migrationBuilder.CreateIndex(
                name: "IX_TerminalFileUploads_MerchantID_TerminalID",
                table: "TerminalFileUploads",
                columns: new[] { "MerchantID", "TerminalID" });

            migrationBuilder.CreateIndex(
                name: "IX_TerminalFileUploads_StartTime",
                table: "TerminalFileUploads",
                column: "StartTime");

            migrationBuilder.CreateIndex(
                name: "IX_TerminalFileUploads_Status",
                table: "TerminalFileUploads",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_TerminalFileUploads_TerminalID_StartTime",
                table: "TerminalFileUploads",
                columns: new[] { "TerminalID", "StartTime" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "TerminalFileUploads");

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreateTime",
                table: "VehicleInfos",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 9, 14, 4, 52, 457, DateTimeKind.Local).AddTicks(2383),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 11, 12, 3, 51, 67, DateTimeKind.Local).AddTicks(465));

            migrationBuilder.AlterColumn<DateTime>(
                name: "OperationTime",
                table: "PasswordChangeLogs",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 9, 14, 4, 52, 456, DateTimeKind.Local).AddTicks(6954),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 11, 12, 3, 51, 66, DateTimeKind.Local).AddTicks(5638));

            migrationBuilder.AlterColumn<DateTime>(
                name: "OperationTime",
                table: "OperationLogs",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 9, 14, 4, 52, 456, DateTimeKind.Local).AddTicks(8231),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 11, 12, 3, 51, 66, DateTimeKind.Local).AddTicks(6693));

            migrationBuilder.AlterColumn<DateTime>(
                name: "UpdatedAt",
                table: "MenuItems",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 9, 14, 4, 52, 457, DateTimeKind.Local).AddTicks(202),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 11, 12, 3, 51, 66, DateTimeKind.Local).AddTicks(8513));

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedAt",
                table: "MenuItems",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 9, 14, 4, 52, 456, DateTimeKind.Local).AddTicks(9875),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 11, 12, 3, 51, 66, DateTimeKind.Local).AddTicks(8167));

            migrationBuilder.AlterColumn<DateTime>(
                name: "UpdatedAt",
                table: "MenuGroups",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 9, 14, 4, 52, 456, DateTimeKind.Local).AddTicks(9101),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 11, 12, 3, 51, 66, DateTimeKind.Local).AddTicks(7464));

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedAt",
                table: "MenuGroups",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 9, 14, 4, 52, 456, DateTimeKind.Local).AddTicks(8845),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 11, 12, 3, 51, 66, DateTimeKind.Local).AddTicks(7229));

            migrationBuilder.AlterColumn<DateTime>(
                name: "OperationTime",
                table: "LoginLogs",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 9, 14, 4, 52, 456, DateTimeKind.Local).AddTicks(5845),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 11, 12, 3, 51, 66, DateTimeKind.Local).AddTicks(4559));
        }
    }
}
