﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SlzrCrossGate.Core.Migrations
{
    /// <inheritdoc />
    public partial class OptimizeFareDiscountSchemeVersionManagement : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<DateTime>(
                name: "CreateTime",
                table: "VehicleInfos",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 14, 17, 13, 14, 246, DateTimeKind.Local).AddTicks(9840),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 14, 16, 27, 21, 442, DateTimeKind.Local).AddTicks(943));

            migrationBuilder.AlterColumn<DateTime>(
                name: "OperationTime",
                table: "PasswordChangeLogs",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 14, 17, 13, 14, 246, DateTimeKind.Local).AddTicks(5149),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 14, 16, 27, 21, 441, DateTimeKind.Local).AddTicks(4355));

            migrationBuilder.AlterColumn<DateTime>(
                name: "OperationTime",
                table: "OperationLogs",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 14, 17, 13, 14, 246, DateTimeKind.Local).AddTicks(6229),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 14, 16, 27, 21, 441, DateTimeKind.Local).AddTicks(5811));

            migrationBuilder.AlterColumn<DateTime>(
                name: "UpdatedAt",
                table: "MenuItems",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 14, 17, 13, 14, 246, DateTimeKind.Local).AddTicks(7959),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 14, 16, 27, 21, 441, DateTimeKind.Local).AddTicks(8244));

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedAt",
                table: "MenuItems",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 14, 17, 13, 14, 246, DateTimeKind.Local).AddTicks(7674),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 14, 16, 27, 21, 441, DateTimeKind.Local).AddTicks(7846));

            migrationBuilder.AlterColumn<DateTime>(
                name: "UpdatedAt",
                table: "MenuGroups",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 14, 17, 13, 14, 246, DateTimeKind.Local).AddTicks(6956),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 14, 16, 27, 21, 441, DateTimeKind.Local).AddTicks(6821));

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedAt",
                table: "MenuGroups",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 14, 17, 13, 14, 246, DateTimeKind.Local).AddTicks(6748),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 14, 16, 27, 21, 441, DateTimeKind.Local).AddTicks(6535));

            migrationBuilder.AlterColumn<DateTime>(
                name: "OperationTime",
                table: "LoginLogs",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 14, 17, 13, 14, 246, DateTimeKind.Local).AddTicks(3973),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 14, 16, 27, 21, 441, DateTimeKind.Local).AddTicks(2885));
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<DateTime>(
                name: "CreateTime",
                table: "VehicleInfos",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 14, 16, 27, 21, 442, DateTimeKind.Local).AddTicks(943),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 14, 17, 13, 14, 246, DateTimeKind.Local).AddTicks(9840));

            migrationBuilder.AlterColumn<DateTime>(
                name: "OperationTime",
                table: "PasswordChangeLogs",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 14, 16, 27, 21, 441, DateTimeKind.Local).AddTicks(4355),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 14, 17, 13, 14, 246, DateTimeKind.Local).AddTicks(5149));

            migrationBuilder.AlterColumn<DateTime>(
                name: "OperationTime",
                table: "OperationLogs",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 14, 16, 27, 21, 441, DateTimeKind.Local).AddTicks(5811),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 14, 17, 13, 14, 246, DateTimeKind.Local).AddTicks(6229));

            migrationBuilder.AlterColumn<DateTime>(
                name: "UpdatedAt",
                table: "MenuItems",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 14, 16, 27, 21, 441, DateTimeKind.Local).AddTicks(8244),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 14, 17, 13, 14, 246, DateTimeKind.Local).AddTicks(7959));

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedAt",
                table: "MenuItems",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 14, 16, 27, 21, 441, DateTimeKind.Local).AddTicks(7846),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 14, 17, 13, 14, 246, DateTimeKind.Local).AddTicks(7674));

            migrationBuilder.AlterColumn<DateTime>(
                name: "UpdatedAt",
                table: "MenuGroups",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 14, 16, 27, 21, 441, DateTimeKind.Local).AddTicks(6821),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 14, 17, 13, 14, 246, DateTimeKind.Local).AddTicks(6956));

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedAt",
                table: "MenuGroups",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 14, 16, 27, 21, 441, DateTimeKind.Local).AddTicks(6535),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 14, 17, 13, 14, 246, DateTimeKind.Local).AddTicks(6748));

            migrationBuilder.AlterColumn<DateTime>(
                name: "OperationTime",
                table: "LoginLogs",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 14, 16, 27, 21, 441, DateTimeKind.Local).AddTicks(2885),
                oldClrType: typeof(DateTime),
                oldType: "datetime(6)",
                oldDefaultValue: new DateTime(2025, 7, 14, 17, 13, 14, 246, DateTimeKind.Local).AddTicks(3973));
        }
    }
}
