import React from 'react';
import { Box, Button, Container, TextField, MenuItem, Grid, Typography, Paper, Chip, IconButton, Tooltip, Dialog, DialogTitle, DialogContent, DialogActions, CircularProgress } from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import { Edit, Delete, Add, Visibility, Refresh, History, CloudDownload, CloudUpload } from '@mui/icons-material';
import { useState, useEffect, useMemo, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { linePriceAPI } from '../../services/api';
import { useSnackbar } from 'notistack';
import MerchantAutocomplete from '../../components/MerchantAutocomplete';
import { useAuth } from '../../contexts/AuthContext';
import { FeatureGuard } from '../../components/FeatureGuard';
import { useFeaturePermission } from '../../hooks/useFeaturePermission';
import { PERMISSIONS } from '../../constants/permissions';
import LinePriceCreateDialog from './LinePriceCreateDialog';
import LinePriceEditDialog from './LinePriceEditDialog';

// 线路参数操作按钮组件
const LinePriceActionButtons = ({ linePrice, onEdit, onDelete, onViewVersions }) => {
  const { user } = useAuth();
  const { checkMultiple } = useFeaturePermission();

  // 批量检查权限
  const permissions = useMemo(() =>
    checkMultiple([
      PERMISSIONS.LINE_PRICE.EDIT,
      PERMISSIONS.LINE_PRICE.DELETE,
      PERMISSIONS.LINE_PRICE.VIEW_VERSIONS
    ]),
    [checkMultiple]
  );

  // 业务逻辑判断
  const canEdit = permissions[PERMISSIONS.LINE_PRICE.EDIT] &&
    (user.roles.includes('SystemAdmin') || linePrice.merchantId === user.merchantId);

  const canDelete = permissions[PERMISSIONS.LINE_PRICE.DELETE] &&
    canEdit && !linePrice.hasPublishedVersions;

  return (
    <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
      {/* 编辑按钮 */}
      <FeatureGuard
        featureKey={PERMISSIONS.LINE_PRICE.EDIT}
        additionalCheck={canEdit}
        fallback={
          <Tooltip title="无编辑权限">
            <span>
              <IconButton size="small" disabled>
                <Edit fontSize="small" />
              </IconButton>
            </span>
          </Tooltip>
        }
      >
        <Tooltip title="编辑">
          <IconButton
            size="small"
            color="primary"
            onClick={onEdit}
          >
            <Edit fontSize="small" />
          </IconButton>
        </Tooltip>
      </FeatureGuard>

      {/* 版本历史按钮 */}
      <FeatureGuard featureKey={PERMISSIONS.LINE_PRICE.VIEW_VERSIONS}>
        <Tooltip title="版本历史">
          <IconButton
            size="small"
            color="info"
            onClick={onViewVersions}
          >
            <History fontSize="small" />
          </IconButton>
        </Tooltip>
      </FeatureGuard>

      {/* 删除按钮 */}
      <FeatureGuard
        featureKey={PERMISSIONS.LINE_PRICE.DELETE}
        additionalCheck={canDelete}
        fallback={
          <Tooltip title={
            !permissions[PERMISSIONS.LINE_PRICE.DELETE]
              ? "无删除权限"
              : linePrice.hasPublishedVersions
                ? "已有发布版本，无法删除"
                : "无编辑权限"
          }>
            <span>
              <IconButton size="small" disabled>
                <Delete fontSize="small" />
              </IconButton>
            </span>
          </Tooltip>
        }
      >
        <Tooltip title="删除">
          <IconButton
            size="small"
            color="error"
            onClick={onDelete}
          >
            <Delete fontSize="small" />
          </IconButton>
        </Tooltip>
      </FeatureGuard>
    </Box>
  );
};

// 票价参数列表组件
const LinePriceListView = () => {
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();
  const { user } = useAuth();

  // 状态管理 - 必须在条件检查之前声明
  const [linePrices, setLinePrices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  const [paginationModel, setPaginationModel] = useState({
    page: 0,
    pageSize: 10,
  });
  // 过滤条件状态
  const [filters, setFilters] = useState({
    selectedMerchant: user?.roles?.includes('SystemAdmin') ? null : user?.merchantId ? { merchantID: user.merchantId } : null,
    lineNumber: '',
    groupNumber: '',
    isActive: '',
  });

  // 导入相关状态
  const [importDialog, setImportDialog] = useState(false);
  const [importFile, setImportFile] = useState(null);
  const [importing, setImporting] = useState(false);
  const [importResult, setImportResult] = useState(null);

  // 弹出窗口状态
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [editingLinePriceId, setEditingLinePriceId] = useState(null);

  // 防重复请求
  const fetchLinePricesRef = useRef();
  const hasLoadedRef = useRef(false);

  // 获取线路票价列表
  const fetchLinePrices = async (isInitialLoad = false, forceLoad = false) => {
    try {
      setLoading(true);

      const params = {
        lineNumber: filters.lineNumber || undefined,
        groupNumber: filters.groupNumber || undefined,
        isActive: filters.isActive === '' ? undefined : filters.isActive === 'true',
        page: paginationModel.page + 1, // DataGrid页码从0开始，API从1开始
        pageSize: paginationModel.pageSize,
      };

      // 新逻辑：只有当 selectedMerchant 和 merchantID 都有效时才添加 merchantId 参数
      if (filters.selectedMerchant && filters.selectedMerchant.merchantID) {
        params.merchantId = filters.selectedMerchant.merchantID;
      }

      // 移除undefined的参数
      Object.keys(params).forEach(key => {
        if (params[key] === undefined) {
          delete params[key];
        }
      });

      // 避免重复请求：检查参数是否真的发生了变化（除非强制加载）
      const paramsString = JSON.stringify(params);
      if (!isInitialLoad && !forceLoad && fetchLinePricesRef.current === paramsString) {
        console.log('LinePriceListView: 参数未变化，跳过重复请求');
        setLoading(false);
        return;
      }
      fetchLinePricesRef.current = paramsString;

      console.log('LinePriceListView: 执行数据请求', params);
      const response = await linePriceAPI.getLinePrices(params);
      setLinePrices(response.items || []);
      setTotalCount(response.totalCount || 0);
    } catch (error) {
      console.error('获取线路票价列表失败:', error);
      enqueueSnackbar('获取线路票价列表失败', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };
  // 统一的数据加载逻辑
  useEffect(() => {
    // 防止重复加载
    if (hasLoadedRef.current) {
      console.log('LinePriceListView: 已加载过，跳过重复请求');
      return;
    }

    console.log('LinePriceListView: 执行首次加载');
    hasLoadedRef.current = true;
    fetchLinePrices(true, false); // 标记为初始加载，非强制加载
  }, []);

  // 当参数变化时重新加载
  useEffect(() => {
    if (hasLoadedRef.current) { // 只有在首次加载完成后才响应参数变化
      console.log('LinePriceListView: 参数变化，重新加载');
      fetchLinePrices(false, false); // 非初始加载，非强制加载
    }
  }, [paginationModel, filters.selectedMerchant]);
  
  // 处理过滤条件变更
  const handleFilterChange = (event) => {
    const { name, value } = event.target;
    setFilters(prev => ({ ...prev, [name]: value }));
  };
  
  // 处理商户选择变更
  const handleMerchantChange = (event, newValue) => {
    setFilters(prev => ({
      ...prev,
      selectedMerchant: newValue // 保存完整的商户对象
    }));
  };
  
  // 应用过滤器
  const applyFilters = () => {
    setPaginationModel(prev => ({ ...prev, page: 0 })); // 重置回第一页
    fetchLinePrices();
  };
  
  // 重置过滤器
  const resetFilters = () => {
    setFilters({
      selectedMerchant: user?.roles?.includes('SystemAdmin') ? null : user?.merchantId ? { merchantID: user.merchantId } : null,
      lineNumber: '',
      groupNumber: '',
      isActive: '',
    });
    setPaginationModel(prev => ({ ...prev, page: 0 }));
  };
  
  // 删除线路票价信息
  const handleDelete = async (id) => {
    if (!window.confirm('确定要删除此线路票价信息吗？')) return;

    try {
      await linePriceAPI.deleteLinePrice(id);
      enqueueSnackbar('删除成功', { variant: 'success' });
      fetchLinePrices();
    } catch (error) {
      console.error('删除失败:', error);
      enqueueSnackbar('删除失败: ' + (error.response?.data?.message || error.message), { variant: 'error' });
    }
  };

  // 弹出窗口处理函数
  const handleCreateDialogOpen = () => {
    setCreateDialogOpen(true);
  };

  const handleCreateDialogClose = () => {
    setCreateDialogOpen(false);
  };

  const handleCreateSuccess = () => {
    fetchLinePrices(false, true); // 强制刷新列表
  };

  const handleEditDialogOpen = (linePriceId) => {
    setEditingLinePriceId(linePriceId);
    setEditDialogOpen(true);
  };

  const handleEditDialogClose = () => {
    setEditDialogOpen(false);
    setEditingLinePriceId(null);
  };

  const handleEditSuccess = () => {
    fetchLinePrices(false, true); // 强制刷新列表
  };

  // 下载导入模板
  const handleDownloadTemplate = async () => {
    try {
      const response = await linePriceAPI.downloadTemplate();
      const url = window.URL.createObjectURL(new Blob([response]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `线路参数导入模板_${new Date().toISOString().slice(0, 10).replace(/-/g, '')}.xlsx`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      enqueueSnackbar('模板下载成功', { variant: 'success' });
    } catch (error) {
      console.error('下载模板失败:', error);
      enqueueSnackbar('下载模板失败: ' + (error.response?.data?.message || error.message), { variant: 'error' });
    }
  };

  // 处理文件选择
  const handleFileChange = (event) => {
    const file = event.target.files[0];
    setImportFile(file);
    setImportResult(null);
  };

  // 执行导入
  const handleImport = async () => {
    if (!importFile) {
      enqueueSnackbar('请选择要导入的文件', { variant: 'warning' });
      return;
    }

    setImporting(true);
    try {
      const formData = new FormData();
      formData.append('file', importFile);

      const response = await linePriceAPI.importLinePrices(formData);
      setImportResult(response);

      if (response.successCount > 0) {
        enqueueSnackbar(`导入完成：成功 ${response.successCount} 条，失败 ${response.failureCount} 条`, {
          variant: response.failureCount > 0 ? 'warning' : 'success'
        });
        fetchLinePrices(); // 刷新列表
      } else {
        enqueueSnackbar('导入失败，请检查文件格式和数据', { variant: 'error' });
      }
    } catch (error) {
      console.error('导入失败:', error);
      const errorMessage = error.response?.data?.message || error.message;
      enqueueSnackbar('导入失败: ' + errorMessage, { variant: 'error' });
      setImportResult({
        totalRows: 0,
        successCount: 0,
        failureCount: 1,
        errors: [errorMessage]
      });
    } finally {
      setImporting(false);
    }
  };

  // 关闭导入对话框
  const handleCloseImportDialog = () => {
    setImportDialog(false);
    setImportFile(null);
    setImportResult(null);
  };
  
  // 定义表格列
  const columns = [
    {
      field: 'merchantName',
      headerName: '商户名称',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={`${params.row.merchantId || params.row.merchantID || '未知'}`} arrow>
          <span style={{ cursor: 'help' }}>
            {params.value}
          </span>
        </Tooltip>
      )
    },
    { field: 'lineNumber', headerName: '线路编号', width: 100 },
    { field: 'groupNumber', headerName: '组号', width: 80 },
    { field: 'lineName', headerName: '线路名称', flex: 1, minWidth: 150 },
    { field: 'branch', headerName: '分公司', width: 120 },
    {
      field: 'fare',
      headerName: '票价(元)',
      width: 100,
      renderCell: (params) => `¥${(params.value / 100).toFixed(2)}`
    },
    { 
      field: 'isActive', 
      headerName: '状态', 
      width: 100,
      renderCell: (params) => (
        <Chip 
          label={params.value ? '启用' : '禁用'} 
          color={params.value ? 'success' : 'default'}
          size="small"
        />
      )
    },
    {
      field: 'currentVersion',
      headerName: '当前版本',
      width: 100
    },
    {
      field: 'currentSchemeName',
      headerName: '当前方案',
      width: 150,
      renderCell: (params) => {
        if (params.value) {
          return (
            <Tooltip title={`当前使用方案：${params.value}`}>
              <Chip
                label={params.value}
                size="small"
                color="primary"
                variant="outlined"
              />
            </Tooltip>
          );
        }
        return <Typography variant="body2" color="text.secondary">-</Typography>;
      }
    },
    {
      field: 'actions',
      headerName: '操作',
      width: 200,
      renderCell: (params) => (
        <LinePriceActionButtons
          linePrice={params.row}
          onEdit={() => handleEditDialogOpen(params.row.id)}
          onViewVersions={() => navigate(`/app/fare-params/${params.row.id}/versions`)}
          onDelete={() => handleDelete(params.row.id)}
        />
      ),
    },
  ];
  return (
    <Container maxWidth="false">
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
        <Box>
          <Typography variant="h4" gutterBottom>
            线路票价参数管理
          </Typography>
          <Typography variant="body2" color="textSecondary">
            管理线路票价参数，支持创建、编辑、删除和查看版本历史
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-start' }}>
          <FeatureGuard featureKey={PERMISSIONS.LINE_PRICE.CREATE}>
            <Button
              variant="outlined"
              startIcon={<CloudDownload />}
              onClick={handleDownloadTemplate}
              disabled={loading}
              sx={{ mr: 1 }}
            >
              下载模板
            </Button>
          </FeatureGuard>
          <FeatureGuard featureKey={PERMISSIONS.LINE_PRICE.CREATE}>
            <Button
              variant="outlined"
              startIcon={<CloudUpload />}
              onClick={() => setImportDialog(true)}
              disabled={loading}
              sx={{ mr: 1 }}
            >
              批量导入
            </Button>
          </FeatureGuard>
          <FeatureGuard featureKey={PERMISSIONS.LINE_PRICE.CREATE}>
            <Button
              variant="contained"
              color="secondary"
              startIcon={<Add />}
              onClick={handleCreateDialogOpen}
            >
              新建
            </Button>
          </FeatureGuard>
        </Box>
      </Box>
      
      {/* 筛选条件 */}      <Paper sx={{ p: 3, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          {user?.roles?.includes('SystemAdmin') && (
            <Grid item xs={12} md={2}>
              <MerchantAutocomplete
                value={filters.selectedMerchant}
                onChange={handleMerchantChange}
                sx={{ width: '100%' }}
                size="medium"
              />
            </Grid>
          )}
          <Grid item xs={12} sm={6} md={2}>
            <TextField
              fullWidth
              label="线路编号"
              name="lineNumber"
              value={filters.lineNumber}
              onChange={handleFilterChange}
              size="medium"
              placeholder="输入4位线路编号"
              inputProps={{ maxLength: 4 }}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <TextField
              fullWidth
              label="组号"
              name="groupNumber"
              value={filters.groupNumber}
              onChange={handleFilterChange}
              size="medium"
              placeholder="输入2位组号"
              inputProps={{ maxLength: 2 }}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <TextField
              fullWidth
              select
              label="状态"
              name="isActive"
              value={filters.isActive}
              onChange={handleFilterChange}
              size="medium"
            >
              <MenuItem value="">全部</MenuItem>
              <MenuItem value="true">启用</MenuItem>
              <MenuItem value="false">禁用</MenuItem>
            </TextField>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                variant="contained"
                onClick={applyFilters}
                startIcon={<Refresh />}
              >
                筛选
              </Button>
              <Button
                variant="outlined"
                onClick={resetFilters}
              >
                重置
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Paper>
      
      {/* 数据表格 */}
      <Paper sx={{ height: 600, width: '100%' }}>
        <DataGrid
          rows={linePrices}
          columns={columns}
          pagination
          paginationMode="server"
          rowCount={totalCount}
          paginationModel={paginationModel}
          onPaginationModelChange={setPaginationModel}
          loading={loading}
          pageSizeOptions={[10, 25, 50]}
          disableRowSelectionOnClick
          disableColumnFilter
          sx={{
            '& .MuiDataGrid-cell:focus': {
              outline: 'none',
            },
          }}
        />
      </Paper>

      {/* 导入对话框 */}
      <Dialog open={importDialog} onClose={handleCloseImportDialog} maxWidth="md" fullWidth>
        <DialogTitle>批量导入线路参数</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
              请选择Excel文件进行导入。文件格式必须与模板一致。
            </Typography>

            <input
              type="file"
              accept=".xlsx"
              onChange={handleFileChange}
              style={{ marginBottom: '16px' }}
            />

            {importFile && (
              <Typography variant="body2" sx={{ mb: 2 }}>
                已选择文件: {importFile.name}
              </Typography>
            )}

            {importing && (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                <CircularProgress size={20} />
                <Typography variant="body2">正在导入...</Typography>
              </Box>
            )}

            {importResult && (
              <Paper sx={{ p: 2, mt: 2, bgcolor: 'background.default' }}>
                <Typography variant="h6" gutterBottom>导入结果</Typography>
                <Typography variant="body2">
                  总行数: {importResult.totalRows} |
                  成功: {importResult.successCount} |
                  失败: {importResult.failureCount}
                </Typography>

                {importResult.errors && importResult.errors.length > 0 && (
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="subtitle2" color="error">错误详情:</Typography>
                    <Box sx={{ maxHeight: 200, overflow: 'auto', mt: 1 }}>
                      {importResult.errors.map((error, index) => (
                        <Typography key={index} variant="body2" color="error" sx={{ fontSize: '0.875rem' }}>
                          {error}
                        </Typography>
                      ))}
                    </Box>
                  </Box>
                )}
              </Paper>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseImportDialog}>取消</Button>
          <Button
            onClick={handleImport}
            variant="contained"
            disabled={!importFile || importing}
          >
            {importing ? '导入中...' : '开始导入'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* 创建线路参数弹出窗口 */}
      <LinePriceCreateDialog
        open={createDialogOpen}
        onClose={handleCreateDialogClose}
        onCreated={handleCreateSuccess}
      />

      {/* 编辑线路参数弹出窗口 */}
      <LinePriceEditDialog
        open={editDialogOpen}
        onClose={handleEditDialogClose}
        onUpdated={handleEditSuccess}
        linePriceId={editingLinePriceId}
      />
    </Container>
  );
};

export default LinePriceListView;
