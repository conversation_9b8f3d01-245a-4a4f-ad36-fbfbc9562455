import React, { useState, useEffect, useMemo, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Container,
  Typography,
  Paper,
  Divider,
  IconButton,
  Tooltip,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  Breadcrumbs,
  Link,
  CircularProgress,
  Checkbox
} from '@mui/material';
import {
  Add,
  Edit,
  Visibility,
  ArrowBack,
  Delete,
  Check,
  ContentCopy,
  FileCopy
} from '@mui/icons-material';
import { DataGrid } from '@mui/x-data-grid';
import { useSnackbar } from 'notistack';
import { linePriceAPI } from '../../services/api';
import { FeatureGuard } from '../../components/FeatureGuard';
import { useFeaturePermission } from '../../hooks/useFeaturePermission';
import { PERMISSIONS } from '../../constants/permissions';
import { useAuth } from '../../contexts/AuthContext';

// 线路参数版本操作按钮组件
const LinePriceVersionActionButtons = ({ version, onEdit, onPublish, onPreview }) => {
  const { user } = useAuth();
  const { checkMultiple } = useFeaturePermission();

  // 批量检查权限
  const permissions = useMemo(() =>
    checkMultiple([
      PERMISSIONS.LINE_PRICE_VERSION.EDIT,
      PERMISSIONS.LINE_PRICE_VERSION.SUBMIT
    ]),
    [checkMultiple]
  );

  // 业务逻辑判断
  const canEdit = permissions[PERMISSIONS.LINE_PRICE_VERSION.EDIT] &&
    (user.roles.includes('SystemAdmin') || version.merchantId === user.merchantId);

  const canSubmit = permissions[PERMISSIONS.LINE_PRICE_VERSION.SUBMIT] &&
    canEdit && version.status === 'draft';

  return (
    <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
      {/* 预览按钮 - 所有用户都可以预览 */}
      <Tooltip title="预览">
        <IconButton
          size="small"
          color="info"
          onClick={() => onPreview(version)}
        >
          <Visibility fontSize="small" />
        </IconButton>
      </Tooltip>

      {/* 编辑按钮 */}
      <FeatureGuard
        featureKey={PERMISSIONS.LINE_PRICE_VERSION.EDIT}
        additionalCheck={canEdit}
        fallback={
          <Tooltip title="无编辑权限">
            <span>
              <IconButton size="small" disabled>
                <Edit fontSize="small" />
              </IconButton>
            </span>
          </Tooltip>
        }
      >
        <Tooltip title="编辑">
          <IconButton
            size="small"
            color="primary"
            onClick={() => onEdit(version)}
          >
            <Edit fontSize="small" />
          </IconButton>
        </Tooltip>
      </FeatureGuard>

      {/* 提交按钮 */}
      <FeatureGuard
        featureKey={PERMISSIONS.LINE_PRICE_VERSION.SUBMIT}
        additionalCheck={canSubmit}
        fallback={
          <Tooltip title={
            !permissions[PERMISSIONS.LINE_PRICE_VERSION.SUBMIT]
              ? "无提交权限"
              : version.status !== 'draft'
                ? "只有草稿状态的版本可以提交"
                : "无编辑权限"
          }>
            <span>
              <IconButton size="small" disabled>
                <Check fontSize="small" />
              </IconButton>
            </span>
          </Tooltip>
        }
      >
        <Tooltip title="提交">
          <IconButton
            size="small"
            color="success"
            onClick={() => onPublish(version)}
          >
            <Check fontSize="small" />
          </IconButton>
        </Tooltip>
      </FeatureGuard>

      {/* 删除功能暂时移除 - 后端未定义相应权限 */}
    </Box>
  );
};

// 线路票价版本列表组件
const LinePriceVersionsView = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();
  const { user } = useAuth();

  // 状态管理 - 必须在条件检查之前声明
  const [linePriceInfo, setLinePriceInfo] = useState(null);
  const [versions, setVersions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  const [paginationModel, setPaginationModel] = useState({
    page: 0,
    pageSize: 10,
  });


  // 预览相关状态
  const [previewDialogOpen, setPreviewDialogOpen] = useState(false);
  const [previewContent, setPreviewContent] = useState(null);
  const [selectedVersionId, setSelectedVersionId] = useState(null);

  // 删除和提交确认对话框状态
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [submitDialogOpen, setSubmitDialogOpen] = useState(false);
  const [actionVersion, setActionVersion] = useState(null);

  // 跨线路复制相关状态
  const [copyToOtherLineDialogOpen, setCopyToOtherLineDialogOpen] = useState(false);
  const [selectedLines, setSelectedLines] = useState([]);
  const [availableLines, setAvailableLines] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [copyingVersionId, setCopyingVersionId] = useState(null);
  const [searchLoading, setSearchLoading] = useState(false);

  // 防重复请求
  const fetchVersionsRef = useRef();
  const hasLoadedRef = useRef(false);
  const linePriceInfoLoadedRef = useRef(false);

  // 加载线路票价基本信息
  const fetchLinePriceInfo = async () => {
    try {
      const data = await linePriceAPI.getLinePrice(id);
      setLinePriceInfo(data);
      document.title = `线路票价 ${data.lineNumber}-${data.groupNumber} 版本管理 - WebAdmin`;
    } catch (error) {
      console.error('获取线路票价信息失败:', error);
      enqueueSnackbar('获取线路票价信息失败', { variant: 'error' });
      navigate('/app/fare-params');
    }
  };

  // 加载版本列表
  const fetchVersions = async (isInitialLoad = false, forceLoad = false) => {
    try {
      setLoading(true);

      const params = {
        page: paginationModel.page + 1,
        pageSize: paginationModel.pageSize
      };

      // 移除undefined的参数
      Object.keys(params).forEach(key => {
        if (params[key] === undefined) {
          delete params[key];
        }
      });

      // 避免重复请求：检查参数是否真的发生了变化（除非强制加载）
      const paramsString = JSON.stringify(params);
      if (!isInitialLoad && !forceLoad && fetchVersionsRef.current === paramsString) {
        console.log('LinePriceVersionsView: 参数未变化，跳过重复请求');
        setLoading(false);
        return;
      }
      fetchVersionsRef.current = paramsString;

      console.log('LinePriceVersionsView: 执行数据请求', params);
      const response = await linePriceAPI.getLinePriceVersions(id, params);
      setVersions(response.items || []);
      setTotalCount(response.totalCount || 0);
    } catch (error) {
      console.error('获取版本列表失败:', error);
      enqueueSnackbar('获取版本列表失败', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  // 初始加载线路信息
  useEffect(() => {
    // 防止重复加载线路信息
    if (linePriceInfoLoadedRef.current) {
      console.log('LinePriceVersionsView: 线路信息已加载过，跳过重复请求');
      return;
    }

    console.log('LinePriceVersionsView: 执行线路信息加载');
    linePriceInfoLoadedRef.current = true;
    fetchLinePriceInfo();
  }, [id]);

  // 统一的数据加载逻辑
  useEffect(() => {
    if (!linePriceInfo) return; // 等待线路信息加载完成

    // 防止重复加载
    if (hasLoadedRef.current) {
      console.log('LinePriceVersionsView: 已加载过，跳过重复请求');
      return;
    }

    console.log('LinePriceVersionsView: 执行首次加载');
    hasLoadedRef.current = true;
    fetchVersions(true, false); // 标记为初始加载，非强制加载
  }, [linePriceInfo]);

  // 当参数变化时重新加载
  useEffect(() => {
    if (hasLoadedRef.current && linePriceInfo) { // 只有在首次加载完成后才响应参数变化
      console.log('LinePriceVersionsView: 参数变化，重新加载');
      fetchVersions(false, false); // 非初始加载，非强制加载
    }
  }, [paginationModel]);

  // 创建新版本
  const handleCreateVersion = () => {
    navigate(`/app/fare-params/${id}/versions/new`);
  };

  // 编辑版本
  const handleEditVersion = (versionId) => {
    navigate(`/app/fare-params/${id}/versions/${versionId}`);
  };
    // 预览文件内容
  const handlePreview = async (versionId) => {
    try {
      setSelectedVersionId(versionId);
      const response = await linePriceAPI.previewLinePriceFile(versionId, {
        merchantId: linePriceInfo.merchantID
      });
      setPreviewContent(response.fileContent);
      setPreviewDialogOpen(true);
    } catch (error) {
      console.error('预览文件失败:', error);
      enqueueSnackbar('预览文件失败', { variant: 'error' });
    }
  };  // 基于现有版本创建新版本
  const handleCopyVersion = async (versionId) => {
    try {
      // 显示加载提示
      enqueueSnackbar('正在复制版本数据...', { variant: 'info' });

      // 直接调用后端的复制创建接口
      const response = await linePriceAPI.copyLinePriceVersion(versionId);
      console.log('复制创建版本成功:', response);

      // 刷新版本列表
      await fetchVersions();

      enqueueSnackbar('复制创建新版本成功', { variant: 'success' });

      // 导航到编辑页面
      if (response && response.id) {
        navigate(`/app/fare-params/${id}/versions/${response.id}`);
      }
    } catch (error) {
      console.error('复制版本失败:', error);
      enqueueSnackbar('复制版本失败: ' + (error.response?.data?.message || error.message), { variant: 'error' });
    }
  };
    // 打开提交确认对话框
  const openSubmitDialog = (versionId, version) => {
    setActionVersion({id: versionId, version: version});
    setSubmitDialogOpen(true);
  };

  // 关闭提交确认对话框
  const closeSubmitDialog = () => {
    setSubmitDialogOpen(false);
    setActionVersion(null);
  };

  // 提交版本
  const handleSubmitVersion = async () => {
    if (!actionVersion) return;

    try {
      await linePriceAPI.submitLinePriceVersion(actionVersion.id, {
        id: actionVersion.id,
        merchantID: linePriceInfo.merchantID
      });
      enqueueSnackbar('提交成功', { variant: 'success' });
      closeSubmitDialog();
      fetchVersions();
    } catch (error) {
      console.error('提交失败:', error);
      enqueueSnackbar('提交失败: ' + (error.response?.data?.message || error.message), { variant: 'error' });
      closeSubmitDialog();
    }
  };

  // 打开删除确认对话框
  const openDeleteDialog = (versionId, version) => {
    setActionVersion({id: versionId, version: version});
    setDeleteDialogOpen(true);
  };

  // 关闭删除确认对话框
  const closeDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setActionVersion(null);
  };

  // 删除版本
  const handleDeleteVersion = async () => {
    if (!actionVersion) return;

    try {
      await linePriceAPI.deleteLinePriceVersion(actionVersion.id);
      enqueueSnackbar('删除成功', { variant: 'success' });
      closeDeleteDialog();
      fetchVersions();
    } catch (error) {
      console.error('删除失败:', error);
      enqueueSnackbar('删除失败: ' + (error.response?.data?.message || error.message), { variant: 'error' });
      closeDeleteDialog();
    }
  };



  // 打开复制到其他线路对话框
  const openCopyToOtherLineDialog = async (versionId) => {
    setCopyingVersionId(versionId);
    setSearchTerm('');
    setSelectedLines([]);
    setAvailableLines([]);
    setCopyToOtherLineDialogOpen(true);
    // 预加载一些线路作为默认选项
    await searchLines('');
  };

  // 搜索线路
  const searchLines = async (term) => {
    try {
      setSearchLoading(true);
      // 调用API搜索线路，排除当前线路
      const response = await linePriceAPI.searchLinePrices({
        search: term,
        excludeLineId: id,
        merchantId: linePriceInfo.merchantID,
        page: 1,
        pageSize: 20
      });
      setAvailableLines(response.items || []);
    } catch (error) {
      console.error('搜索线路失败:', error);
      enqueueSnackbar('搜索线路失败', { variant: 'error' });
    } finally {
      setSearchLoading(false);
    }
  };

  // 处理搜索输入变更
  const handleSearchInputChange = (event) => {
    const value = event.target.value;
    setSearchTerm(value);

    // 添加防抖处理
    const delayDebounceFn = setTimeout(() => {
      searchLines(value);
    }, 500);

    return () => clearTimeout(delayDebounceFn);
  };

  // 处理线路选择变更
  const handleLineSelectionChange = (event, lineId) => {
    if (event.target.checked) {
      setSelectedLines([...selectedLines, lineId]);
    } else {
      setSelectedLines(selectedLines.filter(id => id !== lineId));
    }
  };

  // 执行复制到其他线路
  const handleCopyToOtherLines = async () => {
    if (selectedLines.length === 0) {
      enqueueSnackbar('请至少选择一条目标线路', { variant: 'warning' });
      return;
    }

    try {
      // 显示加载提示
      enqueueSnackbar('正在复制版本数据...', { variant: 'info' });

      // 调用后端API执行复制
      await linePriceAPI.copyLinePriceVersionToOtherLines(copyingVersionId, {
        versionId: copyingVersionId,
        targetLineIds: selectedLines,
        merchantId: linePriceInfo.merchantID
      });

      enqueueSnackbar(`已成功复制到${selectedLines.length}条线路`, { variant: 'success' });
      setCopyToOtherLineDialogOpen(false);
    } catch (error) {
      console.error('复制到其他线路失败:', error);
      enqueueSnackbar('复制失败: ' + (error.response?.data?.message || error.message), { variant: 'error' });
    }
  };

  // 版本状态渲染
  const renderStatus = (status, isPublished) => {
    if (isPublished) {
      return <Chip size="small" color="success" label="已发布" />;
    }

    switch (status) {
      case 0: // Draft
        return <Chip size="small" color="default" label="草稿" />;
      case 1: // Submitted
        return <Chip size="small" color="primary" label="已提交" />;
      default:
        return <Chip size="small" label="未知状态" />;
    }
  };

  // 定义表格列
  const columns = [
    { field: 'version', headerName: '版本号', width: 100 },
    {
      field: 'status',
      headerName: '状态',
      width: 120,
      renderCell: (params) => renderStatus(params.row.status, params.row.isPublished)
    },
    {
      field: 'createTime',
      headerName: '创建时间',
      width: 180,
      valueFormatter: (params) => new Date(params.value).toLocaleString()
    },
    {
      field: 'creator',
      headerName: '创建者',
      width: 120
    },
    {
      field: 'submitTime',
      headerName: '提交时间',
      width: 180,
      valueFormatter: (params) => params.value ? new Date(params.value).toLocaleString() : '-'
    },
    {
      field: 'submitter',
      headerName: '提交者',
      width: 120,
      valueFormatter: (params) => params.value || '-'
    },
    {
      field: 'schemeName',
      headerName: '使用方案',
      width: 150,
      renderCell: (params) => {
        if (params.value) {
          return (
            <Tooltip title={`方案：${params.value}`}>
              <Chip
                label={params.value}
                size="small"
                color="primary"
                variant="outlined"
              />
            </Tooltip>
          );
        }
        return <Typography variant="body2" color="text.secondary">-</Typography>;
      }
    },
    {
      field: 'actions',
      headerName: '操作',
      width: 250,
      renderCell: (params) => (
        <LinePriceVersionActionButtons
          version={params.row}
          onEdit={() => handleEditVersion(params.row.id)}
          onDelete={() => openDeleteDialog(params.row.id, params.row.version)}
          onPublish={() => openSubmitDialog(params.row.id, params.row.version)}
          onPreview={() => handlePreview(params.row.id)}
        />
      ),
    },
  ];

  if (!linePriceInfo) {
    return (
      <Container sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Container>
    );
  }
  return (
    <Container maxWidth="false">
      <Breadcrumbs sx={{ mb: 2 }}>
        <Link color="inherit" href="#" onClick={() => navigate('/app/fare-params')}>
          线路票价管理
        </Link>
        <Typography color="textPrimary">
          {`${linePriceInfo.lineNumber}-${linePriceInfo.groupNumber} ${linePriceInfo.lineName} 版本管理`}
        </Typography>
      </Breadcrumbs>

      <Paper sx={{ p: 3, mb: 3 }}>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Box>
                <Typography variant="h5" gutterBottom>
                  {`${linePriceInfo.lineNumber}-${linePriceInfo.groupNumber} ${linePriceInfo.lineName}`}
                </Typography>
                <Typography color="textSecondary">
                  {`商户: ${linePriceInfo.merchantName} | 当前票价: ${linePriceInfo.fare}分 | 当前版本: ${linePriceInfo.currentVersion || '无'}`}
                </Typography>
              </Box>
              <Box>
                <Button
                  startIcon={<ArrowBack />}
                  onClick={() => navigate('/app/fare-params')}
                  sx={{ mr: 1 }}
                >
                  返回
                </Button>
                <FeatureGuard featureKey={PERMISSIONS.LINE_PRICE_VERSION.CREATE_VERSION}>
                  <Button
                    variant="contained"
                    startIcon={<Add />}
                    onClick={handleCreateVersion}
                  >
                    新建版本
                  </Button>
                </FeatureGuard>
              </Box>
            </Box>
          </Grid>
        </Grid>

        <Divider sx={{ my: 2 }} />

        <Typography variant="h6" gutterBottom>
          版本历史
        </Typography>

        <Box sx={{ height: 500, width: '100%' }}>
          <DataGrid
            rows={versions}
            columns={columns}
            pagination
            paginationMode="server"
            rowCount={totalCount}
            paginationModel={paginationModel}
            onPaginationModelChange={setPaginationModel}
            loading={loading}
            pageSizeOptions={[10, 25, 50]}
            disableRowSelectionOnClick
            disableColumnFilter
            sx={{
              '& .MuiDataGrid-cell:focus': {
                outline: 'none',
              },
            }}
          />
        </Box>
      </Paper>

      {/* 预览对话框 */}
      <Dialog
        open={previewDialogOpen}
        onClose={() => setPreviewDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>文件预览</DialogTitle>
        <DialogContent>
          <Paper
            sx={{
              p: 2,
              maxHeight: '60vh',
              overflowY: 'auto',
              fontFamily: 'monospace',
              fontSize: '0.875rem',
              whiteSpace: 'pre-wrap'
            }}
          >
            {previewContent ? (
              <pre>{JSON.stringify(previewContent, null, 2)}</pre>
            ) : (
              <CircularProgress size={20} />
            )}
          </Paper>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPreviewDialogOpen(false)}>关闭</Button>
        </DialogActions>
      </Dialog>



      {/* 提交确认对话框 */}
      <Dialog
        open={submitDialogOpen}
        onClose={closeSubmitDialog}
        maxWidth="xs"
        fullWidth
        PaperProps={{
          elevation: 3,
          sx: { borderRadius: 2 }
        }}
      >
        <DialogTitle sx={{ pb: 1 }}>确认提交</DialogTitle>
        <DialogContent sx={{ pt: 1 }}>
          <Typography variant="body1">
            确定要提交版本 <b>{actionVersion?.version}</b> 吗？
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            提交后此版本将不能再修改。已提交的版本可用于发布到终端。
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={closeSubmitDialog}>取消</Button>
          <Button
            variant="contained"
            color="secondary"
            onClick={handleSubmitVersion}
            startIcon={<Check />}
          >
            确认提交
          </Button>
        </DialogActions>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog
        open={deleteDialogOpen}
        onClose={closeDeleteDialog}
        maxWidth="xs"
        fullWidth
        PaperProps={{
          elevation: 3,
          sx: { borderRadius: 2 }
        }}
      >
        <DialogTitle sx={{ pb: 1 }} color="error.main">确认删除</DialogTitle>
        <DialogContent sx={{ pt: 1 }}>
          <Typography variant="body1">
            确定要删除版本 <b>{actionVersion?.version}</b> 吗？
          </Typography>
          <Typography variant="body2" color="error" sx={{ mt: 1 }}>
            此操作不可逆，删除后数据将无法恢复。
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={closeDeleteDialog}>取消</Button>
          <Button
            variant="contained"
            color="error"
            onClick={handleDeleteVersion}
            startIcon={<Delete />}
          >
            确认删除
          </Button>
        </DialogActions>
      </Dialog>

      {/* 复制到其他线路对话框 */}
      <Dialog
        open={copyToOtherLineDialogOpen}
        onClose={() => setCopyToOtherLineDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>复制到其他线路</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            margin="normal"
            label="搜索线路"
            value={searchTerm}
            onChange={handleSearchInputChange}
            InputProps={{
              endAdornment: searchLoading ? <CircularProgress size={20} /> : null,
            }}
          />
          <Box sx={{ maxHeight: '300px', overflowY: 'auto' }}>
            {availableLines.map((line) => (
              <Box key={line.id} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Checkbox
                  checked={selectedLines.includes(line.id)}
                  onChange={(event) => handleLineSelectionChange(event, line.id)}
                />
                <Typography>{`${line.lineNumber}-${line.groupNumber} ${line.lineName}`}</Typography>
              </Box>
            ))}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCopyToOtherLineDialogOpen(false)}>取消</Button>
          <Button
            variant="contained"
            onClick={handleCopyToOtherLines}
            startIcon={<FileCopy />}
          >
            复制
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default LinePriceVersionsView;
