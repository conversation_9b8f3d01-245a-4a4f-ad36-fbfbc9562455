import React, { useState, useEffect, useMemo, useRef } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Button,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  MenuItem,
  Chip,
  Tabs,
  Tab
} from '@mui/material';
import {
  ChevronRight as ChevronRight,
  Refresh as RefreshIcon,
  Search as SearchIcon,
  Clear as ClearIcon,
  Delete as DeleteIcon,
  CloudDownload as CloudDownloadIcon,
  History as HistoryIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useSnackbar } from 'notistack';
import { fileAPI } from '../../services/api';
import { formatDateTime } from '../../utils/dateUtils';
import { parseErrorMessage } from '../../utils/errorHandler';
import MerchantAutocomplete from '../../components/MerchantAutocomplete';
import { FeatureGuard } from '../../components/FeatureGuard';
import { useFeaturePermission } from '../../hooks/useFeaturePermission';
import { PERMISSIONS } from '../../constants/permissions';
import { useAuth } from '../../contexts/AuthContext';

// 发布记录操作按钮组件
const PublishRecordActionButtons = ({ record, onDelete, onRetry }) => {
  const { user } = useAuth();
  const { checkMultiple } = useFeaturePermission();

  // 批量检查权限
  const permissions = useMemo(() =>
    checkMultiple([
      PERMISSIONS.PUBLISH_RECORD.DELETE
    ]),
    [checkMultiple]
  );

  // 业务逻辑判断
  const canDelete = permissions[PERMISSIONS.PUBLISH_RECORD.DELETE] &&
    (user.roles.includes('SystemAdmin') || record.merchantId === user.merchantId);

  return (
    <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
      {/* 重试按钮 - 所有用户都可以重试，不需要功能权限控制 */}
      {record.status === 'failed' && (
        <Tooltip title="重试发布">
          <IconButton
            size="small"
            color="warning"
            onClick={() => onRetry(record)}
          >
            <HistoryIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      )}

      {/* 删除按钮 */}
      <FeatureGuard featureKey={PERMISSIONS.PUBLISH_RECORD.DELETE}>
        {canDelete && (
          <Tooltip title="删除记录">
            <IconButton
              size="small"
              color="error"
              onClick={() => onDelete(record)}
            >
              <DeleteIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        )}
      </FeatureGuard>
    </Box>
  );
};

const FilePublishList = () => {
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();
  const { user } = useAuth();

  // 页面访问权限通过菜单权限控制，不再进行页面级别权限检查
  const [tabValue, setTabValue] = useState(0); // 0: 当前发布, 1: 发布历史
  const [filePublishes, setFilePublishes] = useState([]);
  const [fileTypes, setFileTypes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);

  // 筛选条件
  const [filters, setFilters] = useState({
    merchantId: '',
    fileTypeId: '',
    filePara: '',
    publishType: '',
    publishTarget: ''
  });

  // 商户选择状态
  const [selectedMerchant, setSelectedMerchant] = useState(null);

  // 删除确认对话框
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [filePublishToDelete, setFilePublishToDelete] = useState(null);
  const [deleteLoading, setDeleteLoading] = useState(false);

  // 防重复请求
  const loadFilePublishesRef = useRef();
  const hasLoadedRef = useRef(false);

  // 加载文件发布列表
  const loadFilePublishes = async (isInitialLoad = false, forceLoad = false) => {
    try {
      setLoading(true);

      // 构建查询参数
      const params = {
        page: page + 1,
        pageSize: rowsPerPage,
        tabValue, // 包含标签页状态
        ...Object.fromEntries(
          Object.entries(filters).filter(([_, value]) => value !== '')
        )
      };

      // 移除undefined的参数
      Object.keys(params).forEach(key => {
        if (params[key] === undefined) {
          delete params[key];
        }
      });

      // 避免重复请求：检查参数是否真的发生了变化（除非强制加载）
      const paramsString = JSON.stringify(params);
      if (!isInitialLoad && !forceLoad && loadFilePublishesRef.current === paramsString) {
        console.log('FilePublishList: 参数未变化，跳过重复请求');
        setLoading(false);
        return;
      }
      loadFilePublishesRef.current = paramsString;

      console.log('FilePublishList: 执行数据请求', params);
      let response;
      if (tabValue === 0) {
        // 当前发布
        response = await fileAPI.getFilePublishes(params);
      } else {
        // 发布历史
        response = await fileAPI.getFilePublishHistory(params);
      }

      setFilePublishes(response.items);
      setTotalCount(response.totalCount);
    } catch (error) {
      console.error('Error loading file publishes:', error);
      const errorMessage = parseErrorMessage(error, '加载文件发布列表失败');
      enqueueSnackbar(errorMessage, { variant: 'error' });
      setFilePublishes([]);
      setTotalCount(0);
    } finally {
      setLoading(false);
    }
  };

  // 加载文件类型列表
  const loadFileTypes = async () => {
    try {
      // 使用不分页API获取所有文件类型，确保下拉框中包含所有数据
      const response = await fileAPI.getAllFileTypes();
      setFileTypes(response.items || []);
    } catch (error) {
      console.error('Error loading file types:', error);
    }
  };

  // 统一的数据加载逻辑
  useEffect(() => {
    // 防止重复加载
    if (hasLoadedRef.current) {
      console.log('FilePublishList: 已加载过，跳过重复请求');
      return;
    }

    console.log('FilePublishList: 执行首次加载');
    hasLoadedRef.current = true;
    loadFilePublishes(true, false); // 标记为初始加载，非强制加载
    loadFileTypes();
  }, []);

  // 当参数变化时重新加载
  useEffect(() => {
    if (hasLoadedRef.current) { // 只有在首次加载完成后才响应参数变化
      console.log('FilePublishList: 参数变化，重新加载');
      loadFilePublishes(false, false); // 非初始加载，非强制加载
    }
  }, [page, rowsPerPage, tabValue]);

  // 处理筛选条件变更
  const handleFilterChange = (event) => {
    const { name, value } = event.target;
    setFilters(prev => ({ ...prev, [name]: value }));
  };

  // 处理商户选择变更
  const handleMerchantChange = (event, newValue) => {
    setSelectedMerchant(newValue);
    const merchantId = newValue ? newValue.merchantID : '';
    // 商户变更时，清空文件类型选择
    setFilters(prev => ({
      ...prev,
      merchantId,
      fileTypeId: '' // 清空文件类型选择
    }));
  };

  // 应用筛选
  const applyFilters = () => {
    setPage(0);
    loadFilePublishes(false, true); // 强制执行搜索
  };

  // 清除筛选
  const clearFilters = () => {
    setSelectedMerchant(null);
    setFilters({
      merchantId: '',
      fileTypeId: '',
      filePara: '',
      publishType: '',
      publishTarget: ''
    });
    setPage(0);
    loadFilePublishes(false, true); // 强制执行搜索
  };

  // 处理分页变更
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // 处理标签页变更
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
    setPage(0);
  };

  // 打开删除确认对话框
  const openDeleteConfirmDialog = (filePublish) => {
    setFilePublishToDelete(filePublish);
    setOpenDeleteDialog(true);
  };

  // 删除文件发布
  const deleteFilePublish = async () => {
    if (!filePublishToDelete) return;

    setDeleteLoading(true);
    try {
      await fileAPI.deleteFilePublish(filePublishToDelete.id);
      setOpenDeleteDialog(false);
      loadFilePublishes();
      enqueueSnackbar('文件发布删除成功', { variant: 'success' });
    } catch (error) {
      console.error('Error deleting file publish:', error);
      // 使用更友好的错误提示，并处理后端返回的具体错误信息
      let errorMessage = '删除失败';
      if (error.response?.data) {
        if (typeof error.response.data === 'string') {
          errorMessage = error.response.data;
        } else if (error.response.data.message) {
          errorMessage = error.response.data.message;
        }
      }
      enqueueSnackbar(errorMessage, { variant: 'error' });
      // 关闭删除确认对话框
      setOpenDeleteDialog(false);
    } finally {
      setDeleteLoading(false);
    }
  };

  // 下载文件
  const downloadFile = async (fileVersion) => {
      try {
        // 使用fileAPI服务发送带有认证令牌的请求
        const response = await fileAPI.downloadFileVersion(fileVersion.fileVerID);

        // 创建blob URL并触发下载
        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a');

        // 设置下载文件名，组合文件类型、文件参数和版本号
        const merchantId = fileVersion.merchantID;
        const fileTypeId = fileVersion.fileTypeID;
        const filePara = fileVersion.filePara;
        const ver = fileVersion.ver;
        const fileName = `${fileTypeId}${filePara}_${ver}.bin`;

        link.href = url;
        link.setAttribute('download', fileName);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // 记录下载日志
        console.log(`下载文件成功: ID=${fileVersion.id}, 文件名=${fileName}`);
      } catch (error) {
        console.error('下载文件失败:', error);
        alert('下载文件失败，请检查网络连接或联系管理员。');
      }
  };

  // 格式化文件大小
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 获取发布类型显示
  const getPublishTypeChip = (type) => {
    switch (type) {
      case 1: // Merchant
        return <Chip label="商户级别" color="primary" size="small" />;
      case 2: // Line
        return <Chip label="线路级别" color="secondary" size="small" />;
      case 3: // Terminal
        return <Chip label="终端级别" color="success" size="small" />;
      default:
        return <Chip label="未知" color="default" size="small" />;
    }
  };

  // 获取操作类型显示
  const getOperationTypeChip = (type) => {
    switch (type) {
      case "Publish":
        return <Chip label="发布" color="success" size="small" />;
      case "Revoke":
        return <Chip label="撤销" color="error" size="small" />;
      default:
        return <Chip label={type || "未知"} color="default" size="small" />;
    }
  };

  return (
    <Box sx={{ p: 3 }}>

      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
        文件发布管理
        </Typography>
        <Button
          variant="outlined"
          startIcon={<ChevronRight />}
          onClick={() => navigate('/app/files/versions')}
        >
          文件版本管理
        </Button>
      </Box>

      {/* 标签页 */}
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
        >
          <Tab label="当前发布" />
          <Tab label="发布历史" />
        </Tabs>
      </Paper>

      {/* 筛选条件 */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} md={2}>
            <MerchantAutocomplete
              value={selectedMerchant}
              onChange={handleMerchantChange}
              label="商户"
              placeholder="选择商户"
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <TextField
              fullWidth
              label="文件类型"
              name="fileTypeId"
              value={filters.fileTypeId}
              onChange={handleFilterChange}
              size="small"
              select
              disabled={!selectedMerchant} // 未选择商户时禁用
              SelectProps={{
                MenuProps: {
                  PaperProps: {
                    style: {
                      maxHeight: 300, // 设置下拉菜单最大高度
                      overflowY: 'auto', // 启用垂直滚动条
                    },
                  },
                },
              }}
            >
              <MenuItem value="">全部</MenuItem>
              {fileTypes
                // 只显示选中商户的文件类型
                .filter(type => {
                  if (!selectedMerchant) return false;
                  return type.merchantID && type.merchantID === selectedMerchant.merchantID;
                })
                .map((type) => (
                  <MenuItem key={`${type.code}-${type.merchantID}`} value={type.code}>
                    {type.code} - {type.name || '未命名'}
                  </MenuItem>
                ))}
            </TextField>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <TextField
              fullWidth
              label="文件参数"
              name="filePara"
              value={filters.filePara}
              onChange={handleFilterChange}
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <TextField
              fullWidth
              label="发布类型"
              name="publishType"
              value={filters.publishType}
              onChange={handleFilterChange}
              size="small"
              select
            >
              <MenuItem value="">全部</MenuItem>
              <MenuItem value="1">商户级别</MenuItem>
              <MenuItem value="2">线路级别</MenuItem>
              <MenuItem value="3">终端级别</MenuItem>
            </TextField>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <TextField
              fullWidth
              label="发布目标"
              name="publishTarget"
              value={filters.publishTarget}
              onChange={handleFilterChange}
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={1}>
            <Button
              fullWidth
              variant="contained"
              color="primary"
              startIcon={<SearchIcon />}
              onClick={applyFilters}
            >
              搜索
            </Button>
          </Grid>
          <Grid item xs={12} sm={6} md={1}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<ClearIcon />}
              onClick={clearFilters}
            >
              清除
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* 操作按钮 */}
      <Box sx={{ mb: 2 }}>
        <Button
          variant="outlined"
          color="primary"
          startIcon={<RefreshIcon />}
          onClick={loadFilePublishes}
        >
          刷新
        </Button>
      </Box>

      {/* 文件发布列表 */}
      <Paper>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>商户</TableCell>
                <TableCell>文件类型</TableCell>
                <TableCell>文件参数</TableCell>
                <TableCell>版本号</TableCell>
                <TableCell>文件大小</TableCell>
                <TableCell>发布类型</TableCell>
                <TableCell>发布目标</TableCell>
                <TableCell>发布时间</TableCell>
                <TableCell>操作人</TableCell>
                {tabValue === 1 && <TableCell>操作类型</TableCell>}
                {tabValue === 0 && <TableCell>操作</TableCell>}
                {tabValue === 1 && <TableCell>备注</TableCell>}
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={tabValue === 0 ? 10 : 11} align="center">
                    <CircularProgress size={24} />
                  </TableCell>
                </TableRow>
              ) : filePublishes.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={tabValue === 0 ? 10 : 11} align="center">
                    没有找到文件发布记录
                  </TableCell>
                </TableRow>
              ) : (
                filePublishes.map((filePublish) => (
                  <TableRow key={filePublish.id}>
                    <TableCell>
                      <Tooltip title={filePublish.merchantID || ''}>
                          <span>{filePublish.merchantName}</span>
                        </Tooltip>
                    </TableCell>
                    <TableCell>
                      {filePublish.fileTypeName}({filePublish.fileTypeID})
                    </TableCell>
                    <TableCell>{filePublish.filePara}</TableCell>
                    <TableCell>{filePublish.ver}</TableCell>
                    <TableCell>{formatFileSize(filePublish.fileSize)}</TableCell>
                    <TableCell>{getPublishTypeChip(filePublish.publishType)}</TableCell>
                    <TableCell>{filePublish.publishTarget || '-'}</TableCell>
                    <TableCell>{formatDateTime(filePublish.publishTime)}</TableCell>
                    <TableCell>{filePublish.operator || '-'}</TableCell>
                    {tabValue === 1 && (
                      <TableCell>{getOperationTypeChip(filePublish.operationType)}</TableCell>
                    )}
                    {tabValue === 0 && (
                      <TableCell>
                        <PublishRecordActionButtons
                          record={filePublish}
                          onDelete={() => openDeleteConfirmDialog(filePublish)}
                          onRetry={() => retryPublish(filePublish)}
                        />
                      </TableCell>
                    )}
                    {tabValue === 1 && (
                      <TableCell>{filePublish.remark || '-'}</TableCell>
                    )}
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25, 50]}
          component="div"
          count={totalCount}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          labelRowsPerPage="每页行数:"
          labelDisplayedRows={({ from, to, count }) => `${from}-${to} 共 ${count}`}
        />
      </Paper>

      {/* 删除确认对话框 */}
      <Dialog open={openDeleteDialog} onClose={() => setOpenDeleteDialog(false)}>
        <DialogTitle>确认取消发布</DialogTitle>
        <DialogContent>
          <Typography>
            确定要取消发布文件 "{filePublishToDelete?.fileFullType} - {filePublishToDelete?.ver}" 吗？此操作不可撤销。
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDeleteDialog(false)}>取消</Button>
          <Button
            onClick={deleteFilePublish}
            variant="contained"
            color="error"
            disabled={deleteLoading}
          >
            {deleteLoading ? <CircularProgress size={24} /> : '确认取消发布'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default FilePublishList;
