<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <SpaRoot>ClientApp\</SpaRoot>

    <!-- 项目特定配置 -->
    <AssemblyTitle>WebAdmin Terminal Management System</AssemblyTitle>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Controllers\AuthController.cs~RF1f40e6d5.TMP" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="EPPlus" Version="8.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.14" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.UI" Version="8.0.14" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.4">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="NPOI" Version="2.7.3" />
    <PackageReference Include="Otp.NET" Version="1.4.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.0.0" />
    <PackageReference Include="AspNetCore.HealthChecks.MySql" Version="8.0.1" />
    <PackageReference Include="AspNetCore.HealthChecks.RabbitMQ" Version="8.0.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\SlzrCrossGate.Common\SlzrCrossGate.Common.csproj" />
    <ProjectReference Include="..\SlzrCrossGate.Core\SlzrCrossGate.Core.csproj" />
    <ProjectReference Include="..\SlzrCrossGate.ServiceDefaults\SlzrCrossGate.ServiceDefaults.csproj" />
  </ItemGroup>

<!-- 自动构建前端项目并将输出复制到wwwroot目录
  <ItemGroup>
    <Folder Include="wwwroot\" />
  </ItemGroup>

  <Target Name="PublishRunWebpack" AfterTargets="ComputeFilesToPublish">
    <Exec WorkingDirectory="$(SpaRoot)" Command="npm install" />
    <Exec WorkingDirectory="$(SpaRoot)" Command="npm run build" />

    <ItemGroup>
      <DistFiles Include="$(SpaRoot)dist\**" />
      <ResolvedFileToPublish Include="@(DistFiles->'%(FullPath)')" Exclude="@(ResolvedFileToPublish)">
        <RelativePath>%(DistFiles.Identity)</RelativePath>
        <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
        <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      </ResolvedFileToPublish>
    </ItemGroup>
  </Target>
-->

</Project>
