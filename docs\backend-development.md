# 后端开发规范

## 📋 概述

基于实际项目代码分析的 WebAdmin 后端开发规范和技术实现。

## 🛠️ 技术栈

### 核心框架 (基于 .csproj)
```xml
<TargetFramework>net8.0</TargetFramework>
<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.14" />
<PackageReference Include="Microsoft.AspNetCore.Identity.UI" Version="8.0.14" />
<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.4" />
<PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="8.0.3" />
```

### 主要依赖包
```xml
<!-- 认证授权 -->
<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" />
<PackageReference Include="Microsoft.AspNetCore.Identity.UI" />
<PackageReference Include="Otp.NET" Version="1.4.0" />

<!-- 数据访问 -->
<PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="8.0.3" />
<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.4" />

<!-- 文档和健康检查 -->
<PackageReference Include="Swashbuckle.AspNetCore" Version="8.0.0" />
<PackageReference Include="AspNetCore.HealthChecks.MySql" Version="8.0.1" />
<PackageReference Include="AspNetCore.HealthChecks.RabbitMQ" Version="8.0.2" />

<!-- 文件处理 -->
<PackageReference Include="EPPlus" Version="8.0.5" />
<PackageReference Include="NPOI" Version="2.7.3" />
```

## 🏗️ 项目结构

### 实际目录结构
```
SlzrCrossGate.WebAdmin/
├── Controllers/          # API 控制器
│   ├── AuthController.cs
│   ├── UsersController.cs
│   ├── MerchantsController.cs
│   ├── TerminalsController.cs
│   └── ...
├── DTOs/                # 数据传输对象
├── Services/            # 业务服务
├── Filters/             # 过滤器
├── Middleware/          # 中间件
├── Extensions/          # 扩展方法
├── Migrations/          # 数据库迁移
└── Program.cs          # 应用入口
```

## 🗄️ 数据模型

### Entity Framework Core 配置
```csharp
// TcpDbContext.cs - 实际数据上下文
public class TcpDbContext : IdentityDbContext<ApplicationUser, ApplicationRole, string>
{
    public DbSet<Merchant> Merchants { get; set; }
    public DbSet<Terminal> Terminals { get; set; }
    public DbSet<TerminalStatus> TerminalStatuses { get; set; }
    public DbSet<FileVer> FileVers { get; set; }
    public DbSet<FileType> FileTypes { get; set; }
    public DbSet<MsgType> MsgTypes { get; set; }
    public DbSet<MsgContent> MsgContents { get; set; }
    public DbSet<LinePriceInfo> LinePriceInfos { get; set; }
    public DbSet<FareDiscountScheme> FareDiscountSchemes { get; set; }
    public DbSet<UnionPayTerminalKey> UnionPayTerminalKeys { get; set; }
    public DbSet<TerminalFileUpload> TerminalFileUploads { get; set; }
    public DbSet<VehicleInfo> VehicleInfos { get; set; }
    // ... 其他实体
}
```

### 数据库配置
```csharp
// 实际的索引和约束配置
modelBuilder.Entity<FileVer>(builder => {
    builder.HasKey(e => new { e.ID }).IsClustered();
    builder.HasIndex(e => new { e.MerchantID, e.FileFullType, e.Ver });
    builder.HasIndex(e => new { e.MerchantID, e.FileTypeID, e.FilePara, e.Ver });
});

modelBuilder.Entity<TerminalStatus>(builder => {
    builder.Property(e => e.ActiveStatus)
        .HasDefaultValue(DeviceActiveStatus.Active);
    
    // MySQL JSON 字段配置
    if (Database.IsMySql()) {
        builder.Property(e => e.FileVersions).HasColumnType("json");
        builder.Property(e => e.Properties).HasColumnType("json");
    }
});
```

## 🔐 认证与授权

### JWT 认证配置
```csharp
// Program.cs 中的认证配置
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options => {
        options.TokenValidationParameters = new TokenValidationParameters {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = configuration["Jwt:Issuer"],
            ValidAudience = configuration["Jwt:Audience"],
            IssuerSigningKey = new SymmetricSecurityKey(
                Encoding.UTF8.GetBytes(configuration["Jwt:Key"]))
        };
    });
```

### Identity 配置
```csharp
// 用户和角色管理
builder.Services.AddIdentity<ApplicationUser, ApplicationRole>(options => {
    options.Password.RequireDigit = true;
    options.Password.RequiredLength = 6;
    options.Password.RequireNonAlphanumeric = false;
    options.Password.RequireUppercase = false;
    options.Password.RequireLowercase = false;
})
.AddEntityFrameworkStores<TcpDbContext>()
.AddDefaultTokenProviders();
```

## 📡 API 控制器规范

### 标准控制器模式
```csharp
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class MerchantsController : ControllerBase
{
    private readonly TcpDbContext _context;
    private readonly ILogger<MerchantsController> _logger;

    public MerchantsController(TcpDbContext context, ILogger<MerchantsController> logger)
    {
        _context = context;
        _logger = logger;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<MerchantDto>>> GetMerchants()
    {
        // 实现逻辑
    }
}
```

### 实际控制器示例
基于项目中的实际控制器：
- **AuthController** - 认证相关API
- **UsersController** - 用户管理
- **MerchantsController** - 商户管理
- **TerminalsController** - 终端管理
- **FileVersionsController** - 文件版本管理
- **MessagesController** - 消息管理
- **LinePriceController** - 票价管理
- **UnionPayTerminalKeysController** - 银联密钥管理

## 🔧 服务层设计

### 业务服务模式
```csharp
public interface IMerchantService
{
    Task<IEnumerable<MerchantDto>> GetMerchantsAsync();
    Task<MerchantDto> GetMerchantByIdAsync(int id);
    Task<MerchantDto> CreateMerchantAsync(CreateMerchantDto dto);
    Task<MerchantDto> UpdateMerchantAsync(int id, UpdateMerchantDto dto);
    Task DeleteMerchantAsync(int id);
}

public class MerchantService : IMerchantService
{
    private readonly TcpDbContext _context;
    private readonly IMapper _mapper;

    // 实现业务逻辑
}
```

## 📊 数据传输对象 (DTOs)

### DTO 设计模式
```csharp
// 查询 DTO
public class MerchantDto
{
    public int MerchantID { get; set; }
    public string Name { get; set; }
    public string ContactInfo { get; set; }
    public bool IsActive { get; set; }
}

// 创建 DTO
public class CreateMerchantDto
{
    [Required]
    public string Name { get; set; }
    
    [EmailAddress]
    public string Email { get; set; }
}

// 更新 DTO
public class UpdateMerchantDto
{
    public string Name { get; set; }
    public string ContactInfo { get; set; }
    public bool? IsActive { get; set; }
}
```

## 🗃️ 数据库迁移

### Entity Framework Core 迁移
```bash
# 创建迁移
dotnet ef migrations add AddTerminalManagement --project SlzrCrossGate.WebAdmin

# 应用迁移
dotnet ef database update --project SlzrCrossGate.WebAdmin

# 查看迁移历史
dotnet ef migrations list --verbose
```

### 连接字符串配置
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=slzrcrossgate;User=root;Password=password;AllowLoadLocalInfile=true"
  }
}
```

## 🔍 健康检查

### 健康检查配置
```csharp
// Program.cs 中的健康检查配置
builder.Services.AddHealthChecks()
    .AddMySql(connectionString)
    .AddRabbitMQ(rabbitMQConnectionString);

app.MapHealthChecks("/health");
```

## 📝 日志记录

### 结构化日志
```csharp
public class MerchantsController : ControllerBase
{
    private readonly ILogger<MerchantsController> _logger;

    [HttpGet]
    public async Task<ActionResult> GetMerchants()
    {
        _logger.LogInformation("Getting merchants for user {UserId}", User.Identity.Name);
        
        try
        {
            // 业务逻辑
            _logger.LogInformation("Successfully retrieved {Count} merchants", merchants.Count);
            return Ok(merchants);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving merchants");
            return StatusCode(500, "Internal server error");
        }
    }
}
```

## 🛡️ 安全措施

### 数据验证
```csharp
[HttpPost]
public async Task<ActionResult<MerchantDto>> CreateMerchant([FromBody] CreateMerchantDto dto)
{
    if (!ModelState.IsValid)
    {
        return BadRequest(ModelState);
    }

    // 业务逻辑验证
    if (await _context.Merchants.AnyAsync(m => m.Name == dto.Name))
    {
        return Conflict("Merchant with this name already exists");
    }

    // 创建逻辑
}
```

### 权限控制
```csharp
[Authorize(Roles = "Admin")]
public async Task<ActionResult> AdminOnlyAction()
{
    // 仅管理员可访问
}

[Authorize(Policy = "MerchantOwner")]
public async Task<ActionResult> MerchantSpecificAction()
{
    // 商户级权限控制
}
```

## 📦 依赖注入

### 服务注册
```csharp
// Program.cs 中的服务注册
builder.Services.AddDbContext<TcpDbContext>(options =>
    options.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString)));

builder.Services.AddScoped<IMerchantService, MerchantService>();
builder.Services.AddScoped<ITerminalService, TerminalService>();
builder.Services.AddScoped<IFileService, FileService>();

// AutoMapper 配置
builder.Services.AddAutoMapper(typeof(Program));
```

## 🔧 配置管理

### appsettings.json 结构
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=slzrcrossgate;..."
  },
  "Jwt": {
    "Key": "your-secret-key",
    "Issuer": "your-issuer",
    "Audience": "your-audience"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  }
}
```

## 🧪 错误处理

### 全局异常处理
```csharp
public class GlobalExceptionMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<GlobalExceptionMiddleware> _logger;

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An unhandled exception occurred");
            await HandleExceptionAsync(context, ex);
        }
    }

    private static async Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        context.Response.ContentType = "application/json";
        context.Response.StatusCode = 500;

        var response = new
        {
            message = "An error occurred while processing your request.",
            details = exception.Message
        };

        await context.Response.WriteAsync(JsonSerializer.Serialize(response));
    }
}
```

## 📋 版本兼容性

### 重要版本约束
- **.NET 8.0** - 不升级到 .NET 9.0
- **Entity Framework Core 8.0.14** - 不升级到 EF Core 9.x
- **Pomelo.EntityFrameworkCore.MySql 8.0.3** - 仅兼容 EF Core 8.x

### 包管理
```bash
# 安装指定版本的包
dotnet add package PackageName --version X.Y.Z

# 安装 EF Core 工具
dotnet tool install --global dotnet-ef --version 8.0.14
```
